import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import router from '@/router'
import { publicRouterName } from '@/router/publicRouterName'
import { user as userApi, userRole as userRoleApi } from '@/services/api'
import { handleKeepAlive, setRouter } from '@/utils/router'

let selectBidCallback: ((s: string) => void) | null = null

export const useUserStore = defineStore('user', () => {
  const token = ref<string | null>(null)
  const refresh_token = ref<string | null>(null)
  const user = ref<User.Info | null>(null)
  const rememberMe = ref(false)
  const userMenu = ref<Menu.Item[]>([])
  const isNeed2FA = ref(true)
  const permissions = ref<string[]>([])
  // token 过期时间
  const expiresAt = ref<number | null>(null)

  // bid 相关
  const groupList = ref<{ label: string, value: string }[]>([])
  const currentGroup = ref<string[]>([])
  const isLoadingGroupList = ref(false)

  // 多个 bid 新增时需要选择其中一个 bid
  const activeBid = ref<string | null>(null)
  const isShowSelectBid = ref(false)

  const setToken = (newToken: string) => {
    token.value = newToken
  }

  // actions
  const login = async (email: string, password: string, google_token: string, remember: boolean) => {
    try {
      const { data, code } = await userApi.login({ email, password, rememberMe: remember, google_token })

      if (code === 1) {
        throw new Error('Invalid credentials')
      }

      const { access_token, refresh_token: r_token, expires_in = 3600 } = data
      expiresAt.value = Date.now() + expires_in * 1000
      token.value = access_token
      refresh_token.value = r_token
      rememberMe.value = remember
      currentGroup.value = []
      activeBid.value = null
      await getGroupList()
      await getUserInfo()
      await getMenus()
      await getPermissions()
    }
    catch {
      throw new Error('Invalid credentials')
    }
  }

  const register = async (email: string, password: string) => {
    try {
      await userApi.register({ email, password })
    }
    catch {
      throw new Error('Registration failed')
    }
  }

  const forgotPassword = async (email: string) => {
    try {
      await userApi.forgotPassword({ email })
    }
    catch {
      throw new Error('Failed to send password reset email')
    }
  }

  const logout = async () => {
    try {
      await userApi.logout()
    }
    finally {
      currentGroup.value = []
      activeBid.value = null
      setToken('')
      user.value = null
      rememberMe.value = false
      router.replace({ name: publicRouterName.LOGIN })
    }
  }

  const initializeFromStorage = async () => {
    await getGroupList()
    await getUserInfo()
    await getMenus()
    await getPermissions()
  }

  // 转换 menu 菜单
  const transformMenu = (menu: Api.RouterItem): Menu.Item => {
    let redirect: string | { name: string } = menu?.redirect || ''
    if (redirect && !String(redirect)?.startsWith('/')) {
      redirect = {
        name: redirect,
      }
    }
    return {
      path: menu.path,
      name: menu.name,
      redirect,
      children: menu.children?.map(transformMenu) || [],
      meta: {
        isSeparator: menu?.isSeparator === 1,
        breadcrumbTitle: menu?.breadcrumbTitle,
        isHideBreadcrumb: menu?.isHideBreadcrumb === 1,
        keepAlive: menu?.isKeepAlive === 1,
        i18nKey: menu.i18nKey,
        icon: menu.icon,
      },
    }
  }

  // 获取菜单
  const getMenus = async () => {
    const { data } = await userApi.getMenu()
    return new Promise<boolean>((resolve) => {
      const keeps: string[] = []
      // 递归过滤和转换菜单
      const filterAndTransformMenu = (items: Api.RouterItem[]): Menu.Item[] => {
        return items
          .filter((item) => {
            if (item?.isKeepAlive === 1) {
              keeps.push(item.name)
            }
            return !item.isHide
          })
          .map((item) => {
            const menuItem = transformMenu(item)
            if (item.children && item.children.length > 0) {
              menuItem.children = filterAndTransformMenu(item.children)
            }
            return menuItem
          })
      }
      // add redirect route
      const redirectRoute = {
        id: 99999,
        parent_id: 0,
        i18nKey: 'menu.customers',
        label: null,
        component: 'views/redirect.vue',
        icon: '',
        name: publicRouterName.REDIRECT,
        path: '/redirect/:path(.*)',
        isHide: 1,
        breadcrumbTitle: '',
        isKeepAlive: 0,
        isHideBreadcrumb: 0,
        isSeparator: 0,
      } as Api.RouterItem
      // 设置路由
      setRouter([...data, redirectRoute])
      // 设置菜单
      userMenu.value = filterAndTransformMenu([...data, redirectRoute])
      // 设置 keepAlive 名称
      handleKeepAlive(keeps)
      resolve(true)
    })
  }

  const getPermissions = async () => {
    const { data, code } = await userRoleApi.getPermissionList()
    if (code === 0) {
      permissions.value = data.map(item => item.slug)
    }
  }

  const getUserInfo = async (): Promise<User.Info> => {
    const data = await userApi.getUserInfo()
    isNeed2FA.value = data.code === 403
    user.value = data.data
    return user.value
  }

  const updateUserInfo = async (updateData: User.UserInfoUpdateReq) => {
    const { data, code } = await userApi.updateUserInfo(updateData)
    if (code === 0) {
      user.value = data
    }
    return user.value
  }

  const getGroupList = async () => {
    if (isLoadingGroupList.value) {
      return
    }
    isLoadingGroupList.value = true
    try {
      const { data } = await userApi.getGroupList()

      const newGroupList = []
      for (const key in data.business_id_list) {
        newGroupList.push({
          label: data.business_id_list[key] || key,
          value: key,
        })
      }
      groupList.value = newGroupList

      if (currentGroup.value.length === 0) {
        currentGroup.value = newGroupList.map(item => item.value)
      }
      else {
        currentGroup.value.forEach((item) => {
          if (!Object.keys(data.business_id_list || {}).includes(item)) {
            currentGroup.value = currentGroup.value.filter(i => i !== item)
          }
        })
      }
    }
    finally {
      isLoadingGroupList.value = false
    }
  }

  const showSelectBid = (callback?: (s: string) => void) => {
    if (currentGroup.value.length === 1) {
      callback?.(currentGroup.value[0] || '')
    }
    else {
      isShowSelectBid.value = true
      selectBidCallback = callback || null
    }
  }

  const confirmSelectBid = () => {
    console.log(111)

    isShowSelectBid.value = false
    selectBidCallback?.(activeBid.value || '')
    currentGroup.value = [activeBid.value || '']
    selectBidCallback = null
  }

  // 权限检查方法
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => permissions.value.includes(permission))
  }

  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => permissions.value.includes(permission))
  }

  // getters
  const isLoggedIn = computed(() => !!token.value)
  const currentUsername = computed(() => user.value?.name)
  const userPermissions = computed(() => permissions.value)

  return {
    // state
    token,
    user,
    rememberMe,
    userMenu,
    isNeed2FA,
    expiresAt,
    setToken,
    userPermissions,

    // bid
    groupList,
    currentGroup,
    isLoadingGroupList,
    activeBid,
    isShowSelectBid,

    // actions
    login,
    register,
    forgotPassword,
    logout,
    initializeFromStorage,
    getMenus,
    getUserInfo,
    updateUserInfo,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getGroupList,
    confirmSelectBid,
    showSelectBid,

    // getters
    isLoggedIn,
    currentUsername,
  }
}, {
  persist: {
    omit: ['userMenu', 'isLogin', 'user', 'isNeed2FA', 'configData', 'groupList', 'isShowSelectBid', 'isLoadingGroupList', 'permissions'],
  },
})
