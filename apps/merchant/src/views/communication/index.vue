<script setup lang="ts">
import type { THEME_COLOR_MAP } from './theme'
import Skeleton from 'primevue/skeleton'
import { useToast } from 'primevue/usetoast'
import { nextTick, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { getCustomerConfig, updateCommunicationTheme } from '@/services/api/customer'
import { updateNotification } from '@/services/api/notification'
import Edit from './component/edit.vue'
import emailPreview from './component/emailPreview.vue'
import { pickColor } from './theme'

const headlineColor = ref<keyof typeof THEME_COLOR_MAP | string>('light_orange')

const previewLogo = ref<any>([])

const template = ref('')
const toast = useToast()
const { t } = useI18n()
const isLoading = ref(true)

const editRef = ref<InstanceType<typeof Edit>>()

const isSubmitLoading = ref(false)

const updateSelectedColor = (color: string) => {
  headlineColor.value = color
}

const updateContent = (content: string) => {
  template.value = content
}

const updateTheme = async () => {
  isSubmitLoading.value = true
  try {
    const { code } = await updateCommunicationTheme({
      logo: previewLogo.value[0].url,
      theme: headlineColor.value,
    })
    if (code === 0) {
      toast.add({
        severity: 'success',
        summary: t('common.success', 'Success'),
        detail: 'Successfully updated',
      })
      fetchConfig()
    }
  }
  catch (error: any) {
    console.error('Update theme error:', error)
  }
  finally {
    isSubmitLoading.value = false
  }
}

const updateNotificationType = async (type: number) => {
  isLoading.value = true
  try {
    const sendData = {
      notification_type: type,
      content: null,
    }
    const { code } = await updateNotification(sendData)
    if (code === 0) {
      toast.add({
        severity: 'success',
        summary: t('common.success', 'Success'),
        detail: 'Successfully updated',
      })
    }
  }
  catch (error: any) {
    console.error('Update notification error:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error', 'Error'),
      detail: t('common.operationFailed', 'Operation failed'),
    })
  }
  finally {
    isLoading.value = false
  }
}

const fetchConfig = async () => {
  isLoading.value = true
  try {
    const { code, data } = await getCustomerConfig()
    if (code === 0) {
      isLoading.value = false
      previewLogo.value = [{ url: data.logo }]
      headlineColor.value = data.theme
      nextTick(() => {
        if (editRef.value) {
          editRef.value.selectedColors.themeKey = data.theme as keyof typeof THEME_COLOR_MAP
        }
      })
    }
  }
  catch (error: any) {
    isLoading.value = false
    console.error('Fetch config error:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error', 'Error'),
      detail: t('common.fetchFailed', 'Failed to fetch data'),
      life: 3000,
    })
  }
}

fetchConfig()
</script>

<template>
  <div class="notification">
    <div class="notification-container flex flex-col lg:flex-row justify-between items-start gap-4">
      <!-- Left side - Edit section -->
      <div class="notification-edit w-full lg:w-3/5">
        <!-- Skeleton loading for edit section -->
        <template v-if="isLoading">
          <div class="title-container mb-6 pb-4 border-[#d8d8d8] border-b-2">
            <Skeleton class="mb-2" height="40px" width="80%" />
            <Skeleton class="mb-2" height="20px" width="100%" />
            <Skeleton class="mb-2" height="20px" width="90%" />
          </div>
          <div class="edit-skeleton-container">
            <Skeleton class="mb-4" height="60px" width="100%" />
            <Skeleton class="mb-4" height="50px" width="40%" />
            <Skeleton class="mb-6" height="100px" width="100%" />
            <Skeleton class="mb-4" height="200px" width="100%" />
            <Skeleton class="mb-4" height="50px" width="30%" border-radius="16px" />
          </div>
        </template>

        <!-- Actual edit content -->
        <template v-else>
          <div class="title-container mb-6 pb-4 border-[#d8d8d8] border-b-2">
            <div class="title text-[26.6px] font-extrabold mb-2">
              Brand Your Experience
            </div>
            <div class="subtitle text-[#545454]">
              Create a professional payment experience - customise how your payment pages and email notifications will
              appear to customers.
            </div>
          </div>
          <Edit
            ref="editRef"
            v-model:logo="previewLogo" :submit-loading="isSubmitLoading"
            @update:selected-color="updateSelectedColor" @update:change-content="updateContent"
            @update:change-template-type="updateNotificationType" @update-theme="updateTheme"
            @cancel-theme="fetchConfig"
          />
        </template>
      </div>

      <!-- Right side - Preview section -->
      <div class="notification-preview w-full lg:w-2/5">
        <!-- Skeleton loading for preview section -->
        <template v-if="isLoading">
          <Skeleton class="mb-4" height="40px" width="50%" />
          <div class="preview-skeleton">
            <Skeleton class="mb-4" height="60px" width="100%" />
            <Skeleton class="mb-4" height="250px" width="100%" border-radius="8px" />
          </div>
        </template>

        <!-- Actual preview content -->
        <template v-else>
          <div class="text-[26.6px] font-extrabold mb-2">
            Preview
          </div>
          <emailPreview
            :header-style="pickColor(headlineColor)" :logo-src="previewLogo" :content="template"
            :submit-loading="isSubmitLoading"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.notification-edit,
.notification-preview {
  background-color: var(--color-white);
  border-radius: 16px;
  padding: 24px;
}

.edit-skeleton-container,
.preview-skeleton {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  :deep(.p-skeleton) {
    background-color: var(--surface-200);

    &::after {
      background: linear-gradient(90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.4) 50%,
          rgba(255, 255, 255, 0) 100%);
    }
  }
}
</style>
