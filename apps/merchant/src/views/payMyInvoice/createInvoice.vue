<script setup lang="ts">
import { ref } from 'vue'
import CreateInvoiceForm from './components/createInvoiceForm.vue'

// import CreateInvoicePreview from './components/createInvoicePreview.vue'
import { useCreateInvoice } from './composables/useCreateInvoice'

const { formData, handleSubmit, schema, config, options, loadings, updateCustomers } = useCreateInvoice('add')

const formWrapperRef = ref<InstanceType<typeof CreateInvoiceForm>>()
</script>

<template>
  <div v-if="$route.name === 'payMyInvoiceCreateInvoice'" class="flex justify-between bg-white rounded-2xl p-4">
    <div
      class="w-full" :class="{
        'md:w-230 ': !(loadings.loadingCustomers),
      }"
    >
      <div v-if="loadings.loadingCustomers" class="flex justify-center items-center min-h-[200px]">
        <div class="flex flex-col items-center gap-2">
          <ProgressSpinner />
          <span class="text-gray-500">Loading...</span>
        </div>
      </div>
      <CreateInvoiceForm v-else ref="formWrapperRef" :form-data="formData" :schema="schema" :config="config" :options-loading="loadings" :options="options" :loadings="loadings" @submit="handleSubmit" @update-customer="updateCustomers" />
    </div>
    <!-- class="lg:w-3/5" -->
    <!-- <div class="lg:w-2/5">
      <CreateInvoicePreview :form-data="formData" />
    </div> -->
  </div>
  <router-view />
</template>

<style scoped></style>
