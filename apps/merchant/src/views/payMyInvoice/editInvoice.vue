<script setup lang="ts">
import { ref } from 'vue'
import CreateInvoiceForm from './components/createInvoiceForm.vue'

import { useCreateInvoice } from './composables/useCreateInvoice'

const { formData, handleEditSubmit, schema, config, options, loadings, updateCustomers } = useCreateInvoice('edit')

const formWrapperRef = ref<InstanceType<typeof CreateInvoiceForm>>()
</script>

<template>
  <div class="flex bg-white rounded-2xl p-4">
    <div
      v-if="loadings.loadingDetail || loadings.loadingCustomers"
      class="flex justify-center items-center min-h-[200px] w-full"
    >
      <div class="flex justify-center items-center">
        <div class="flex flex-col items-center gap-2">
          <ProgressSpinner />
          <span class="text-gray-500">Loading...</span>
        </div>
      </div>
    </div>
    <CreateInvoiceForm
      v-else ref="formWrapperRef" mode="edit" :config="config" :form-data="formData" :schema="schema"
      :options-loading="loadings" :options="options" :loadings="loadings" @submit="handleEditSubmit"
      @update-customer="updateCustomers"
    />
  </div>
</template>
