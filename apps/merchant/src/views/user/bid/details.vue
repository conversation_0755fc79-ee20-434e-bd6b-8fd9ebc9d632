<script setup lang="ts">
import Button from 'primevue/button'
import Message from 'primevue/message'
import ProgressSpinner from 'primevue/progressspinner'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseTag from '@/components/common/BaseTag.vue'
import { user as userApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'userBidDetails',
})

const route = useRoute()
const router = useRouter()
const toast = useToast()
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')
const businessDetail = ref<User.BusinessDetailRes | null>(null)

// Get status text
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return 'Enabled'
    case 2:
      return 'Disabled'
    case 3:
      return 'Business Terms Configuration'
    default:
      return 'Unknown'
  }
}

// Get status severity
const getStatusSeverity = (status: number) => {
  switch (status) {
    case 1:
      return 'paid'
    case 2:
      return 'failed'
    case 3:
      return 'upcoming'
    default:
      return 'default'
  }
}

// Get settlement type text
const getSettlementTypeText = (type: number) => {
  switch (type) {
    case 1:
      return 'Daily Settlement'
    case 2:
      return 'Monthly Settlement'
    default:
      return 'Unknown'
  }
}

// Get BPAY switch text
const getBpaySwitchText = (bpaySwitch: number) => {
  return bpaySwitch === 1 ? 'Enabled' : 'Disabled'
}

// Format fee display
const formatFee = (feeItems: User.FeeItem[]) => {
  if (!feeItems || feeItems.length === 0) { return 'N/A' }
  return feeItems.map((item) => {
    if (item.fee_rate === '1') {
      return `${item.fee_value}%`
    }
    else {
      return `$${item.fee_value}`
    }
  }).join(', ')
}

const fetchBusinessDetail = async () => {
  const id = route.params.id as string
  if (!id) {
    error.value = true
    errorMessage.value = 'Business is required'
    loading.value = false
    return
  }

  loading.value = true
  error.value = false

  try {
    const response = await userApi.getBusinessDetail(id)
    businessDetail.value = response.data
  }
  catch (err: any) {
    error.value = true
    errorMessage.value = err.message || 'Failed to load business details'
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: err.message || 'Failed to load business details',
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// Go back to list
const goBack = () => {
  router.back()
}

onMounted(() => {
  fetchBusinessDetail()
})
</script>

<template>
  <div class="business-details">
    <!-- Header -->
    <div class="card mb-4">
      <div class="card-header">
        <div class="flex items-center justify-between">
          <h2>Business Detail</h2>
          <Button
            label="Back"
            severity="secondary"
            icon="pi pi-arrow-left"
            @click="goBack"
          />
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <ProgressSpinner stroke-width="3" />
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex flex-col items-center gap-3 p-4">
      <Message severity="error" :closable="false" class="w-full">
        {{ errorMessage }}
      </Message>
      <Button label="Retry" icon="pi pi-refresh" @click="fetchBusinessDetail" />
    </div>

    <!-- Business Detail Content -->
    <div v-else-if="businessDetail" class="space-y-6">
      <!-- Basic Information -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Basic Information
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="info-item">
              <label class="info-label">Merchant ID</label>
              <div class="info-value">
                {{ businessDetail.merchant_id }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Business</label>
              <div class="info-value">
                {{ businessDetail.merchant_name }} - {{ businessDetail.business_id }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Status</label>
              <div class="info-value">
                <BaseTag :text="getStatusText(businessDetail.status)" :type="getStatusSeverity(businessDetail.status)" />
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Settlement Type</label>
              <div class="info-value">
                {{ getSettlementTypeText(businessDetail.settlement_type) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">BPAY Switch</label>
              <div class="info-value">
                <BaseTag
                  :text="getBpaySwitchText(businessDetail.bpay_switch)"
                  :type="businessDetail.bpay_switch === 1 ? 'paid' : 'failed'"
                />
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Created Date</label>
              <div class="info-value">
                {{ formatDate(businessDetail.created_at) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Updated Date</label>
              <div class="info-value">
                {{ formatDate(businessDetail.updated_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Account Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Account Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="info-item">
              <label class="info-label">Account Setup Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.account_fees.account_setup_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Account Termination Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.account_fees.account_termination_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Minimum Monthly Transaction Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.account_fees.minimum_monthly_transaction_fee) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Integration Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Integration Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="info-item">
              <label class="info-label">Integration Setup Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.integration_fees.integration_setup_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Integration Customisation & Development Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.integration_fees.integration_customisation_and_development_fee) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Platform Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Platform Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="info-item">
              <label class="info-label">User Licence Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.platform_fees.user_licence_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Extended Helpdesk Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.platform_fees.extended_helpdesk_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Hosting & Maintenance Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.platform_fees.hosting_maintenance_fee) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bank Account Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Bank Account Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="info-item">
              <label class="info-label">Per Transaction Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.bank_account_fees.per_transaction_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Per Dishonour Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.bank_account_fees.per_dishonour_fee) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Credit Card Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Credit Card Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="info-item">
              <label class="info-label">Per Transaction Standard Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.per_transaction_standard_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Per Transaction Premium Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.per_transaction_premium_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Domestic VISA Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.domestic_visa_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">Domestic Mastercard Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.domestic_mastercard_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">International VISA Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.international_visa_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">International Mastercard Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.international_mastercard_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">EFTPOS Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.credit_card_fees.eftpos_fee) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- BPAY Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            BPAY Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 gap-6">
            <div class="info-item">
              <label class="info-label">Per Transaction Standard Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.bpay_fees.per_transaction_standard_fee) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Other Fees -->
      <div class="card">
        <div class="card-header">
          <h3 class="section-title">
            Other Fees
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="info-item">
              <label class="info-label">Surcharge Fee</label>
              <div class="info-value">
                {{ formatFee(businessDetail.surcharge_fees.surcharge_fee) }}
              </div>
            </div>
            <div class="info-item">
              <label class="info-label">GST</label>
              <div class="info-value">
                {{ formatFee(businessDetail.gst.gst) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.business-details {
  min-height: 100vh;
  background-color: var(--surface-ground);
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.card-body {
  padding: 1.5rem;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: var(--text-color);
}

.info-item {
  margin-bottom: 1rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-secondary);
  margin-bottom: 0.25rem;
}

.info-value {
  color: var(--text-color);
  font-size: 0.875rem;
}

@media screen and (max-width: 768px) {
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .info-label {
    width: 100%;
  }
}
</style>
