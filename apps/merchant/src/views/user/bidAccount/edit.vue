<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as yup from 'yup'
import { useListRefresh } from '@/composables/useListRefresh'
import { user as userApi } from '@/services/api'

defineOptions({
  name: 'userBidAccountEdit',
})

const router = useRouter()
const route = useRoute()
const formRef = ref()
const loading = ref(false)
const fetchLoading = ref(false)
const businessLoading = ref(false)
const businessOptions = ref<{ label: string, value: string }[]>([])

const { backWithRefresh } = useListRefresh('userBidAccountList', () => {})

// 表单初始值
const initialValues = reactive({
  business_id: '',
  bsb: '',
  account_no: '',
  account_name: '',
})

// 表单验证规则
const schema = toTypedSchema(
  yup.object({
    business_id: yup.string().required('Business is required'),
    bsb: yup.string()
      .required('BSB is required')
      .matches(/^\d{6}$/, 'BSB must be 6 digits'),
    account_no: yup.string().required('Account number is required'),
    account_name: yup.string().required('Account name is required'),
  }),
)

// 获取账户详情
const fetchAccountDetail = async () => {
  const id = route.params.id as string
  if (!id) { return }

  fetchLoading.value = true
  try {
    const { data, code } = await userApi.getBidAccountDetail(id)
    if (code === 0) {
      // 更新表单初始值
      initialValues.business_id = data.business_id
      initialValues.bsb = data.bsb
      initialValues.account_no = data.account_no
      initialValues.account_name = data.account_name
    }
  }
  catch (error: any) {
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message || 'Failed to fetch account details',
      life: 3000,
    })
  }
  finally {
    fetchLoading.value = false
  }
}

// 提交表单
const onSubmit = async (values: any) => {
  const id = route.params.id as string
  if (!id) { return }

  loading.value = true
  try {
    const { code } = await userApi.updateBidAccount({
      id,
      ...values,
    })

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Bid account updated successfully',
      })
      backWithRefresh()
    }
  }
  catch (error: any) {
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message || 'Failed to update bid account',
    })
  }
  finally {
    loading.value = false
  }
}

// 取消操作
const onCancel = () => {
  router.back()
}

// 获取业务列表
const fetchBusinessList = async () => {
  businessLoading.value = true
  try {
    const response = await userApi.getBusinessList()
    businessOptions.value = response.data.data.map((business: User.BusinessInfo) => ({
      label: `${business.business_name} - ${business.business_id}`,
      value: business.business_id,
    }))
  }
  catch (error: any) {
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message || 'Failed to fetch business list',
    })
  }
  finally {
    businessLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  await Promise.all([
    fetchBusinessList(),
    fetchAccountDetail(),
  ])
})
</script>

<template>
  <div class="bid-account-edit-page bg-white rounded-2xl px-6 p-8">
    <!-- loading -->
    <div v-if="fetchLoading" class="flex">
      <ProgressSpinner />
    </div>
    <VeeForm
      v-else
      ref="formRef"
      class="max-w-2xl"
      :validation-schema="schema"
      :initial-values="initialValues"
      @submit="onSubmit"
    >
      <div class="card-body">
        <div class="grid grid-cols-1 gap-6">
          <!-- Business -->
          <div class="field">
            <label for="business_id" class="block font-medium text-gray-700 mb-2">
              Business <span class="text-red-500">*</span>
            </label>
            <Field
              id="business_id"
              v-slot="{ field, errorMessage, handleChange }"
              name="business_id"
            >
              <Select
                v-model="field.value"
                :class="{ 'p-invalid': errorMessage }"
                class="w-full"
                :options="businessOptions"
                option-label="label"
                option-value="value"
                placeholder="Select Business"
                :loading="businessLoading"
                :disabled="businessLoading"
                @value-change="handleChange"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>

          <!-- BSB -->
          <div class="field">
            <label for="bsb" class="block font-medium text-gray-700 mb-2">
              BSB <span class="text-red-500">*</span>
            </label>
            <Field
              id="bsb"
              v-slot="{ field, errorMessage }"
              name="bsb"
            >
              <InputText
                v-bind="field"
                :class="{ 'p-invalid': errorMessage }"
                class="w-full"
                placeholder="Enter 6-digit BSB number (e.g., 123456)"
                maxlength="6"
                @input="(e) => {
                  // Only allow digits
                  const target = e.target as HTMLInputElement
                  if (target) {
                    const value = target.value.replace(/\D/g, '')
                    target.value = value
                    field.value = value
                  }
                }"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>

          <!-- Account Number -->
          <div class="field">
            <label for="account_no" class="block font-medium text-gray-700 mb-2">
              Account Number <span class="text-red-500">*</span>
            </label>
            <Field
              id="account_no"
              v-slot="{ field, errorMessage }"
              name="account_no"
            >
              <InputText
                v-bind="field"
                :class="{ 'p-invalid': errorMessage }"
                class="w-full"
                placeholder="Enter account number"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>

          <!-- Account Name -->
          <div class="field">
            <label for="account_name" class="block font-medium text-gray-700 mb-2">
              Account Name <span class="text-red-500">*</span>
            </label>
            <Field
              id="account_name"
              v-slot="{ field, errorMessage }"
              name="account_name"
            >
              <InputText
                v-bind="field"
                :class="{ 'p-invalid': errorMessage }"
                class="w-full"
                placeholder="Enter account name"
              />
              <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
                {{ errorMessage }}
              </Message>
            </Field>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <div class="flex justify-end gap-3">
          <Button
            type="button"
            label="Cancel"
            severity="secondary"
            @click="onCancel"
          />
          <Button
            type="submit"
            label="Update"
            :loading="loading"
            severity="warn"
          />
        </div>
      </div>
    </VeeForm>
  </div>
</template>

<style lang="scss" scoped>
.field {
  margin-bottom: 1.5rem;
}

.field:last-child {
  margin-bottom: 0;
}
</style>
