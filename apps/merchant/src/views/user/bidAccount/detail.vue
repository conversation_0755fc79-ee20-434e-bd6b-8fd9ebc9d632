<script setup lang="ts">
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { user as userApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'userBidAccountDetail',
})

const toast = useToast()
const route = useRoute()
const router = useRouter()

const loading = ref(false)
const account = ref<Api.BidAccountDetailRes | null>(null)

// 获取账户详情
const fetchAccountDetail = async () => {
  const id = route.params.id as string
  if (!id) { return }

  loading.value = true
  try {
    const response = await userApi.getBidAccountDetail(id)
    account.value = response.data
  }
  catch (error: any) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message || 'Failed to fetch account details',
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return 'Approved'
    case 2:
      return 'Rejected'
    case 3:
      return 'Pending'
    default:
      return 'Unknown'
  }
}

// 获取状态样式
const getStatusSeverity = (status: number) => {
  switch (status) {
    case 1:
      return 'paid' // 通过/启用
    case 2:
      return 'failed' // 拒绝
    case 3:
      return 'pending' // 待审核
    default:
      return 'pending'
  }
}

// 返回列表
const goBack = () => {
  router.push('/user/bidAccount/list')
}

// 编辑账户
const editAccount = () => {
  if (account.value) {
    router.push(`/user/bidAccount/edit/${account.value.id}`)
  }
}

// 状态更新
const updateAccountStatus = async (type: string) => {
  if (!account.value) { return }

  try {
    await userApi.updateBidAccountStatus({
      id: account.value.id.toString(),
      type,
    })
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Account status updated successfully',
      life: 3000,
    })
    // 重新获取详情
    await fetchAccountDetail()
  }
  catch (error: any) {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: error.message || 'Failed to update account status',
      life: 3000,
    })
  }
}

onMounted(() => {
  fetchAccountDetail()
})
</script>

<template>
  <div class="bid-account-detail-page">
    <div class="card">
      <div class="card-header">
        <div class="flex items-center justify-between">
          <h2>Bid Account Detail</h2>
          <div class="flex gap-2">
            <Button
              label="Back"
              severity="secondary"
              icon="pi pi-arrow-left"
              @click="goBack"
            />
            <Button
              v-if="account"
              label="Edit"
              severity="warn"
              icon="pi pi-pencil"
              @click="editAccount"
            />
          </div>
        </div>
      </div>

      <div v-if="loading" class="card-body">
        <div class="flex justify-center items-center py-8">
          <ProgressSpinner />
        </div>
      </div>

      <div v-else-if="account" class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Basic Information -->
          <div class="detail-section">
            <h3 class="section-title">
              Basic Information
            </h3>

            <div class="detail-item">
              <label>Business</label>
              <div class="value">
                {{ account.business_id }}
              </div>
            </div>

            <div class="detail-item">
              <label>Account Name</label>
              <div class="value">
                {{ account.account_name }}
              </div>
            </div>

            <div class="detail-item">
              <label>BSB</label>
              <div class="value">
                {{ account.bsb }}
              </div>
            </div>

            <div class="detail-item">
              <label>Account Number</label>
              <div class="value">
                {{ account.account_no }}
              </div>
            </div>

            <div class="detail-item">
              <label>Status</label>
              <div class="value">
                <BaseTag :text="getStatusText(account.status)" :type="getStatusSeverity(account.status)" />
              </div>
            </div>
          </div>

          <!-- User Information -->
          <div class="detail-section">
            <h3 class="section-title">
              User Information
            </h3>

            <div class="detail-item">
              <label>Creator</label>
              <div class="value">
                {{ account.user?.name || '-' }}
              </div>
            </div>

            <div class="detail-item">
              <label>Reviewer</label>
              <div class="value">
                {{ account.reviewer?.name || '-' }}
              </div>
            </div>

            <div class="detail-item">
              <label>Admin</label>
              <div class="value">
                {{ account.admin?.name || '-' }}
              </div>
            </div>

            <div class="detail-item">
              <label>Created Date</label>
              <div class="value">
                {{ formatDate(account.created_at) }}
              </div>
            </div>

            <div class="detail-item">
              <label>Updated Date</label>
              <div class="value">
                {{ formatDate(account.updated_at) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Status Actions -->
        <div v-if="account.status === 3" class="mt-6 pt-6 border-t border-gray-200">
          <h3 class="section-title">
            Actions
          </h3>
          <div class="flex gap-3">
            <Button
              label="Approve"
              severity="success"
              icon="pi pi-check"
              @click="updateAccountStatus('3')"
            />
            <Button
              label="Reject"
              severity="danger"
              icon="pi pi-times"
              @click="updateAccountStatus('1')"
            />
          </div>
        </div>
      </div>

      <div v-else class="card-body">
        <div class="text-center py-8">
          <p class="text-gray-500">
            Account not found
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bid-account-detail-page {
  padding: 1rem;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.card-body {
  padding: 1.5rem;
}

.detail-section {
  background: #f9fafb;
  border-radius: 6px;
  padding: 1.25rem;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.detail-item {
  margin-bottom: 1rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.detail-item .value {
  font-size: 0.875rem;
  color: #111827;
  font-weight: 500;
}
</style>
