<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import { toTypedSchema } from '@vee-validate/yup'
import { useToast } from 'primevue/usetoast'
import { Field, Form } from 'vee-validate'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { user as userApi } from '@/services/api'
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'

defineOptions({
  name: 'userBidAccountList',
})

const toast = useToast()

// 使用 useRequestList 处理BID账户列表数据
const requestList = useRequestList<User.BidAccountInfo, Api.BidAccountListReq>({
  requestFn: userApi.getBidAccountList,
})

const router = useRouter()

const userStore = useUserStore()

const { hasPermission, hasAnyPermission } = usePermissions()

const {
  list,
  loading,
  total,
  refresh,
  setSearchParams,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

// 使用通用的列表刷新逻辑
useListRefresh('userBidAccountList', refresh)

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: '',
    header: 'Business',
    style: { minWidth: '200px' },
    template: 'businessName',
  },
  {
    field: 'account_name',
    header: 'Account Name',
    style: { minWidth: '150px' },
  },
  {
    field: 'bsb',
    header: 'BSB',
  },
  {
    field: 'account_no',
    header: 'Account Number',
    style: { minWidth: '150px' },
  },
  {
    field: 'status',
    header: 'Status',
    style: { minWidth: '100px' },
    template: 'status',
  },
  {
    field: 'user',
    header: 'Creator',
    style: { minWidth: '120px' },
    template: 'creator',
  },
  {
    field: 'reviewer',
    header: 'Reviewer',
    style: { minWidth: '120px' },
    template: 'reviewer',
  },
  {
    field: 'created_at',
    header: 'Created Date',
    style: { minWidth: '150px' },
    sortable: true,
    template: 'date',
    sortField: 'created_at',
  },
  {
    field: 'action',
    header: '',
    style: { width: '50px' },
    template: 'action',
    alignFrozen: 'right',
    frozen: true,
  },
])

const auditDialog = ref(false)

const auditLoading = ref(false)

const statusOptions = ref<DictItem[]>([])

const auditOptions = ref<DictItem[]>([
  {
    label: 'Approve',
    value: 3,
  },
  {
    label: 'Reject',
    value: 1,
  },
])

const searchModel = ref<Partial<Api.BidAccountListReq>>({
  status: undefined,
})

const auditForm = ref<{
  type: number
  id: number
  business_id: string
}>({
  type: 0,
  id: 0,
  business_id: '',
})

const auditSchema = toTypedSchema(
  yup.object({
    type: yup.number().required('Type is required'),
  }),
)

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    defaultValue: '',
  },
])

// 排序处理
const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  requestList.setParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  requestList.search()
}

// 获取状态样式
const getStatusSeverity = (status: number) => {
  switch (status) {
    case 1:
      return 'default' // 通过/启用
    case -1:
    case -2:
      return 'failed' // 拒绝
    case 3:
      return 'paid' // 待审核
    default:
      return 'paid'
  }
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

// 导航到创建页面
const navigateToCreate = () => {
  router.push({
    name: 'userBidAccountCreate',
  })
}

// 导航到编辑页面
const navigateToEdit = (data: User.BidAccountInfo) => {
  router.push({
    name: 'userBidAccountEdit',
    params: {
      id: data.id,
    },
  })
}

// 删除相关
const deleteDialog = ref(false)
const selectedAccount = ref<User.BidAccountInfo | null>(null)

const confirmDelete = (data: User.BidAccountInfo) => {
  selectedAccount.value = data
  deleteDialog.value = true
}

const deleteAccount = async () => {
  if (!selectedAccount.value) { return }

  try {
    await userApi.deleteBidAccount({ id: selectedAccount.value.id.toString() })
    toast.add({
      severity: 'success',
      summary: 'Success',
      detail: 'Bid account deleted successfully',
    })
    deleteDialog.value = false
    selectedAccount.value = null
    refresh()
  }
  catch {
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to delete bid account',
    })
  }
}

const handleUpdate = (data: User.BidAccountInfo) => {
  auditDialog.value = true
  auditForm.value = {
    business_id: data.business_id,
    id: data.id,
    type: 3,
  }
}

const handleCancel = (data: User.BidAccountInfo) => {
  window.$confirm.require({
    header: 'Cancel Settlement Account',
    message: 'Are you sure you want to cancel this Settlement Account?',
    acceptLabel: 'Cancel',
    accept: async () => {
      const { code } = await userApi.updateBidAccountStatus({
        id: data.id.toString(),
        type: '2',
      })
      if (code === 0) {
        window.$toast.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Bid account cancelled successfully',
        })
        refresh()
      }
    },
  })
}

const updateAccount = async (values: any) => {
  console.log(values)
  auditLoading.value = true
  try {
    const { code } = await userApi.updateBidAccountStatus({ ...values })
    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Bid account updated successfully',
      })
      auditDialog.value = false
      refresh()
    }
  }
  catch {
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to update bid account',
    })
  }
  finally {
    auditLoading.value = false
  }
}

const { getLabel: getStatusLabel } = useDict('business_account_status', (res) => {
  statusOptions.value = addAllToDict(res)
})
</script>

<template>
  <div class="bid-account-list-page">
    <BaseSearch
      v-model="searchModel"
      :loading="loading"
      :basic-search-fields="searchFields"
      @search="handleSearch"
    />

    <div class="flex items-center gap-2 md:gap-4 mb-5 px-2 md:px-4 lg:px-8">
      <Button
        label="Add New Settlement Account"
        class="!px-8"
        severity="warn"
        icon="pi pi-plus"
        @click="navigateToCreate"
      />
    </div>

    <BaseDataTable
      :row-hover="false"
      :columns="columns"
      :value="list"
      :loading="loading"
      :total-records="total"
      :paginator="true"
      :rows="50"
      :lazy="true"
      data-key="id"
      :show-search-bar="false"
      :scrollable="true"
      :show-multiple-column="false"
      search-placeholder="Search Settlement Account ..."
      :failed="failed"
      :failure-message="failureMessage"
      :striped-rows="true"
      @sort="handleSort"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
    >
      <template #businessName="{ data }">
        <!-- no wrap line -->
        <div class="no-wrap">
          {{ data?.business?.business_name }} - {{ data?.business?.business_id }}
        </div>
      </template>
      <template #status="{ data }">
        <BaseTag :text="getStatusLabel(data.status)" :type="getStatusSeverity(data.status)" />
      </template>
      <template #creator="{ data }">
        <div>{{ data.user?.name || '-' }}</div>
      </template>
      <template #reviewer="{ data }">
        <div>{{ data.reviewer?.name || '-' }}</div>
      </template>
      <template #date="{ data }">
        <div>{{ formatDate(data.created_at) }}</div>
      </template>
      <template #action="{ data }">
        <BaseDataTableActions
          v-if="hasAnyPermission([
            Permissions.USER_UPDATE,
            Permissions.USER_DELETE,
          ])"
        >
          <div class="flex gap-4">
            <Button v-if="hasPermission(Permissions.USER_UPDATE) && data?.status !== 3" severity="secondary" @click="navigateToEdit(data)">
              Edit
            </Button>
            <Button v-if="hasPermission(Permissions.USER_DELETE)" severity="secondary" @click="confirmDelete(data)">
              Delete
            </Button>
            <Button v-if="userStore.user?.type === 0 && data?.status === 1" label="Review" @click="handleUpdate(data)" />
            <!-- 作废 -->
            <Button v-if="userStore.user?.type === 0 && data?.status !== -2 " label="CANCEL" @click="handleCancel(data)" />
          </div>
        </BaseDataTableActions>
      </template>
    </BaseDataTable>

    <!-- 删除确认对话框 -->
    <Dialog
      v-model:visible="deleteDialog"
      :style="{ width: '450px' }"
      header="Confirm Delete"
      :modal="true"
    >
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem" />
        <span v-if="selectedAccount">
          Are you sure you want to delete the bid account "{{ selectedAccount.account_name }}"?
        </span>
      </div>
      <template #footer>
        <Button label="No" icon="pi pi-times" text @click="deleteDialog = false" />
        <Button label="Yes" icon="pi pi-check" text @click="deleteAccount" />
      </template>
    </Dialog>

    <!-- audit dialog -->
    <Dialog
      v-if="auditDialog"
      v-model:visible="auditDialog"
      :style="{ width: '450px' }"
      header="Review New Settlement Account"
      :modal="true"
    >
      <Form :initial-values="auditForm" :validation-schema="auditSchema" @submit="updateAccount">
        <Field v-slot="{ field, errorMessage, handleChange }" class="mb-4" as="div" name="type" label="type">
          <label for="type" class="block font-medium text-gray-700 mb-2">
            Type
          </label>
          <Select v-model="field.value" :options="auditOptions" option-label="label" option-value="value" class="w-full" @value-change="handleChange" />
          <Message v-if="errorMessage" class="mt-2" severity="error" variant="simple">
            {{ errorMessage }}
          </Message>
        </Field>
        <div class="flex gap-2 justify-end">
          <Button label="No" icon="pi pi-times" :loading="auditLoading" @click="auditLoading = false" />
          <Button label="Yes" icon="pi pi-check" :loading="auditLoading" type="submit" />
        </div>
      </Form>
    </Dialog>
  </div>
</template>

<style scoped>
.p-dialog .p-dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.p-dialog .p-dialog-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
  text-align: right;
}

.p-dialog .p-dialog-content {
  padding: 2rem;
}

.field {
  margin-bottom: 1.5rem;
}

.confirmation-content {
  display: flex;
  align-items: center;
}
</style>
