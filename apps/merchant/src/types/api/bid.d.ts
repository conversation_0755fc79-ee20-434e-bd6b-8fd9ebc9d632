declare namespace Api {
  /**
   * Request payload for getting business/bid list
   */
  interface BidListReq {
    /**
     * 页码
     */
    'page'?: number
    'page_size'?: number
    /**
     * 排序字段
     */
    'sort_by'?: string
    /**
     * 排序方向
     */
    'sort_order'?: 'asc' | 'desc'
    /**
     * 业务名称，支持模糊搜索
     */
    'merchant_name'?: string
    'business_name'?: string
    'location'?: string
    'business_id'?: string
    'settlement_type'?: string
    'status'?: string
    'bpay_switch'?: string
    'created_at[]': string[]
  }

  /**
   * Request payload for getting bid account list
   */
  interface BidAccountListReq {
    /**
     * 状态
     */
    status?: number
    /**
     * 页码
     */
    page?: number
    /**
     * 条数
     */
    page_size?: number
    /**
     * 排序字段
     */
    sort_by?: string
    /**
     * 排序方向
     */
    sort_order?: 'asc' | 'desc'
  }

  /**
   * Request payload for creating a new business/bid
   */
  interface BidCreateReq {
    business_name: string
    location: string
  }

  /**
   * Request payload for updating a business/bid
   */
  interface BidUpdateReq {
    name: string
    description: string
  }

  /**
   * Request payload for creating bid account
   */
  interface BidAccountCreateReq {
    business_id: string
    bsb: string
    account_no: string
    account_name: string
  }

  /**
   * Request payload for updating bid account
   */
  interface BidAccountUpdateReq {
    id: string
    business_id: string
    bsb: string
    account_no: string
    account_name: string
  }

  /**
   * Request payload for deleting bid account
   */
  interface BidAccountDeleteReq {
    id: string
  }

  /**
   * Request payload for updating bid account status
   */
  interface BidAccountStatusUpdateReq {
    type: string // 1: 拒绝 2: 取消 3: 通过
    id: string
  }

  /**
   * Response from the business/bid detail endpoint
   */
  interface BidDetailRes {
    id: number
    name: string
    description: string
    created_at: string
    updated_at: string
  }

  /**
   * Response from the bid account detail endpoint
   */
  interface BidAccountDetailRes {
    id: number
    merchant_id: string
    business_id: string
    bsb: string
    account_no: string
    account_name: string
    status: number
    user_id: number
    reviewer_id: number
    admin_id: number
    created_at: string
    updated_at: string
    user: {
      id: number
      name: string
    }
    reviewer: {
      id: number
      name: string
    }
    admin: {
      id: number
      name: string
    }
  }
}
