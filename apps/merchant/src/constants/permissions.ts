export enum Permissions {
  // Home Dashboard
  HOME_GET_SUM_DATA = 'merchantHomeGetSumData',
  HOME_GET_PAYMENT_DATA = 'merchantHomeGetPaymentData',
  HOME_GET_BUSINESS_PERFORMANCE_SUMMARY = 'merchantHomeGetBusinessPerformanceSummary',

  // Customer Management
  CUSTOMER_GET_CUSTOMER_ID = 'merchantCustomerGetCustomerId',
  CUSTOMER_CREATE = 'merchantCustomerCreate',
  CUSTOMER_SEND_INVITE_MAIL = 'merchantCustomerSendInviteCustomerMail',
  CUSTOMER_UPDATE = 'merchantCustomerUpdate',
  CUSTOMER_DELETE = 'merchantCustomerDelete',
  CUSTOMER_DETAIL = 'merchantCustomerDetail',
  CUSTOMER_LIST = 'merchantCustomerList',
  CUSTOMER_EXPORT = 'merchantCustomerExport',
  CUSTOMER_COMMUNICATION_HISTORY = 'merchantCustomerCommunicationHistory',
  CUSTOMER_NOTIFICATION_SWITCH = 'merchantCustomerNotificationSwitch',

  // Invitation Management
  INVITATION_SUBMIT = 'merchantInvitationSubmit',
  INVITATION_INFO = 'merchantInvitationInfo',

  // Plan Management
  PLAN_CREATE = 'merchantPlanCreate',
  PLAN_UPDATE = 'merchantPlanUpdate',
  PLAN_DETAIL = 'merchantPlanDetail',
  PLAN_LIST = 'merchantPlanList',
  PLAN_DELETE = 'merchantPlanDelete',
  PLAN_EXPORT = 'merchantPlanExport',

  // User Profile & Settings
  GET_USER_INFO = 'merchantGetUserInfo',
  USER_UPDATE_PASSWORD = 'merchantUserUpdatePassword',
  GET_MERCHANT_CONFIG = 'merchantGetMerchantConfig',

  // Role Management
  ROLE_LIST = 'merchantRoleList',
  ROLE_DETAIL = 'merchantRoleDetail',
  ROLE_CREATE = 'merchantRoleCreate',
  ROLE_UPDATE = 'merchantRoleUpdate',
  ROLE_DELETE = 'merchantRoleDelete',

  // User Management
  USER_LIST = 'merchantUserList',
  USER_DETAIL = 'merchantUserDetail',
  USER_CREATE = 'merchantUserCreate',
  USER_UPDATE = 'merchantUserUpdate',
  AUTH_USER_UPDATE = 'merchantAuthUserUpdate',
  USER_DELETE = 'merchantUserDelete',

  // Transaction Management
  TRANS_LIST = 'merchantTransList',
  TRANS_DETAIL = 'merchantTransDetail',
  TRANS_REFUND = 'merchantTransRefund',
  TRANS_CREATE = 'merchantTransCreate',
  TRANS_EXPORT = 'merchantTransExport',

  // Reports
  REPORT_REVENUE = 'merchantReportRevenue',
  REPORT_REVENUE_DETAIL = 'merchantReportRevenueDetail',
  REPORT_TRANSACTION_PAYOUT = 'merchantReportTransactionPayout',
  REPORT_SUBSCRIPTION = 'merchantReportSubscription',

  // Subscription Management
  SUBSCRIPTION_CREATE = 'merchantSubscriptionCreate',
  SUBSCRIPTION_UPDATE = 'merchantSubscriptionUpdate',
  SUBSCRIPTION_DETAIL = 'merchantSubscriptionDetail',
  SUBSCRIPTION_CANCEL = 'merchantSubscriptionCancel',
  SUBSCRIPTION_SEND_MOD_PAYMENT_METHOD_MAIL = 'merchantSubscriptionSendModPaymentMethodMail',
  SUBSCRIPTION_GET_MOD_PAYMENT_METHOD_INFO = 'merchantSubscriptionGetModPaymentMethodInfo',
  SUBSCRIPTION_MOD_PAYMENT_METHOD = 'merchantSubscriptionModPaymentMethod',

  // Payout Management
  PAYOUT_LIST = 'merchantPayoutList',

  // Communication Configuration
  COMMUNICATION_CONFIG_DETAIL = 'merchantCommunicationConfigDetail',
  COMMUNICATION_CONFIG_LIST = 'merchantCommunicationConfigList',
  COMMUNICATION_CONFIG_UPDATE = 'merchantCommunicationConfigUpdate',

  // Download Center
  DOWNLOAD_CENTER_LIST = 'merchantDownloadCenterList',
  DOWNLOAD_CENTER_UPLOAD = 'merchantDownloadCenterUpload',

  // Official Services
  OFFICIAL_MERCHANT_QUESTIONNAIRE = 'officialMerchantQuestionnaire',
  OFFICIAL_PAYER_SUPPORT = 'officialPayerSupport',
  OFFICIAL_BILLER_SUPPORT = 'officialBillerSupport',
  OFFICIAL_CONTACT_MESSAGE = 'officialContactMessage',
  OFFICIAL_CUSTOMER_FEEDBACK = 'officialCustomerFeedback',

  // Dictionary & Configuration
  GET_DICT_DATA_CONST = 'merchantGetDictDataConst',
  GET_COUNTRY = 'merchantGetCountry',

  // Xero Integration
  XERO_SUBMIT = 'merchantXeroSubmit',
  XERO_BREAK = 'merchantXeroBreak',
  XERO_INFO = 'merchantXeroInfo',
  XERO_REFRESH_ACCESS_TOKEN = 'merchantXeroRefreshAccessToken',
  XERO_ADD_CONTACT = 'merchantXeroAddContact',
  XERO_UPDATE_CONTACT = 'merchantXeroUpdateContact',
  XERO_GET_CONTACT_LIST = 'merchantXeroGetContactList',
  XERO_GET_CONTACT_DETAIL = 'merchantXeroGetContactDetail',
  XERO_ADD_ACCOUNT = 'merchantXeroAddAccount',
  XERO_UPDATE_ACCOUNT = 'merchantXeroUpdateAccount',
  XERO_GET_ACCOUNT_LIST = 'merchantXeroGetAccountList',
  XERO_GET_ACCOUNT_DETAIL = 'merchantXeroGetAccountDetail',
  XERO_DELETE_ACCOUNT = 'merchantXeroDeleteAccount',
  XERO_ADD_INVOICE = 'merchantXeroAddInvoice',
  XERO_UPDATE_INVOICE = 'merchantXeroUpdateInvoice',
  XERO_GET_INVOICE_LIST = 'merchantXeroGetInvoiceList',
  XERO_GET_INVOICE_DETAIL = 'merchantXeroGetInvoiceDetail',
  XERO_NOTIFY = 'merchantXeroNotify',
  XERO_GET_ALL_ACCOUNT_CODE = 'merchantXeroGetAllAccountCode',
  XERO_GET_ALL_CUSTOMER = 'merchantXeroGetAllCustomer',
  XERO_DELETE_INVOICE = 'merchantXeroDeleteInvoice',
  XERO_GET_THEME_LIST = 'merchantXeroGetThemeList',
  XERO_GET_INVOICE_PAYMENT_DETAIL = 'merchantXeroGetInvoicePaymentDetail',
  XERO_SEND_INVOICE_EMAIL = 'merchantXeroSendInvoiceEmail',
  XERO_INVOICE_PAYMENT_SUBMIT = 'merchantXeroInvoicePaymentSubmit',
  XERO_SYNC_CHANNEL_DATA = 'merchantXeroSyncChannelData',
  XERO_GET_INVOICE_STATUS = 'merchantXeroGetInvoiceStatus',
  XERO_GET_ACCOUNT_CONFIG = 'merchantXeroGetAccountConfig',
  XERO_UPDATE_BRANDING_THEME_CONFIG = 'merchantXeroUpdateBrandingThemeConfig',
  XERO_UPDATE_INVOICE_CHANNEL_CONFIG = 'merchantXeroUpdateInvoiceChannelConfig',
  XERO_CREATE_CUSTOMER = 'merchantXeroCreateCustomer',

  // Support
  SUPPORT = 'merchantSupport',
}
