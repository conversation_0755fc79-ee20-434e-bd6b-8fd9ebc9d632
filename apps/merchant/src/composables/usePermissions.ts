import { computed } from 'vue'
import { Permissions } from '@/constants/permissions'
import { useUserStore } from '@/store/modules/user'

/**
 * 权限管理组合式函数 - 用于按钮和UI元素的权限控制
 * 提供权限检查的响应式方法，支持按钮显示/隐藏、启用/禁用等场景
 */
export function usePermissions() {
  const userStore = useUserStore()

  /**
   * 检查是否拥有指定权限
   * @param permission 权限字符串或权限枚举
   * @returns 是否拥有权限
   */
  const hasPermission = (permission: string | Permissions): boolean => {
    const permissionStr = String(permission)
    return userStore.hasPermission(permissionStr)
  }

  /**
   * 检查是否拥有任意一个权限
   * @param permissions 权限列表
   * @returns 是否拥有任意一个权限
   */
  const hasAnyPermission = (permissions: (string | Permissions)[]): boolean => {
    const permissionStrs = permissions.map(p => String(p))
    return userStore.hasAnyPermission(permissionStrs)
  }

  /**
   * 检查是否拥有所有权限
   * @param permissions 权限列表
   * @returns 是否拥有所有权限
   */
  const hasAllPermissions = (permissions: (string | Permissions)[]): boolean => {
    const permissionStrs = permissions.map(p => String(p))
    return userStore.hasAllPermissions(permissionStrs)
  }

  /**
   * 响应式的权限检查
   * @param permission 权限字符串或权限枚举
   * @returns 响应式的权限状态
   */
  const canAccess = (permission: string | Permissions) => {
    return computed(() => hasPermission(permission))
  }

  /**
   * 响应式的多权限检查（任意一个）
   * @param permissions 权限列表
   * @returns 响应式的权限状态
   */
  const canAccessAny = (permissions: (string | Permissions)[]) => {
    return computed(() => hasAnyPermission(permissions))
  }

  /**
   * 响应式的多权限检查（所有）
   * @param permissions 权限列表
   * @returns 响应式的权限状态
   */
  const canAccessAll = (permissions: (string | Permissions)[]) => {
    return computed(() => hasAllPermissions(permissions))
  }

  /**
   * 获取用户所有权限
   */
  const userPermissions = computed(() => userStore.userPermissions)

  /**
   * 检查是否已登录
   */
  const isLoggedIn = computed(() => userStore.isLoggedIn)

  return {
    // 基础权限检查方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,

    // 响应式权限检查
    canAccess,
    canAccessAny,
    canAccessAll,

    // 权限数据
    userPermissions,
    isLoggedIn,

    // 权限常量（方便使用）
    Permissions,
  }
}

/**
 * 权限检查装饰器函数
 * 用于方法级别的权限控制
 */
export function requirePermission(permission: string | Permissions) {
  return function (_: any, __: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function (...args: any[]) {
      const { hasPermission } = usePermissions()

      if (!hasPermission(permission)) {
        console.warn(`Access denied: Missing permission ${permission}`)
        return
      }

      return originalMethod.apply(this, args)
    }

    return descriptor
  }
}
