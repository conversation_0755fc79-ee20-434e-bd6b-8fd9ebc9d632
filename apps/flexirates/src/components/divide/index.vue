<script setup lang="ts">
</script>

<template>
  <div class="divider-container">
    <div class="line" />
    <div class="dots">
      <div class="dot dot-red" />
      <div class="dot dot-blue" />
    </div>
  </div>
</template>

<style scoped>
.divider-container {
  width: 100%;
  position: relative;
  height: 2px;
  margin: 20px 0;

}

.line {
  width: calc(100% - 30px);
  height: 2px;
  background-color: #fe4c1c;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.dots {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 4px;
  padding-left: 10px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.dot-red {
  background-color: #fe4c1c;
}

.dot-blue {
  background-color: #09deff;
}
</style>
