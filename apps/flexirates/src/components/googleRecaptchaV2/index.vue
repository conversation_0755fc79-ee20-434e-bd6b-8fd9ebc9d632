<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

const props = withDefaults(defineProps<Props>(), {
  siteKey: import.meta.env.VITE_GOOGLE_RECAPTCHA_V2 as string,
  theme: 'light',
  size: 'normal',
  tabindex: 0,
})

const emit = defineEmits<{
  (e: 'verify', response: string): void
  (e: 'expired'): void
  (e: 'error'): void
}>()

const { locale } = useI18n()

interface Props {
  siteKey?: string
  theme?: 'light' | 'dark'
  size?: 'normal' | 'compact'
  tabindex?: number
}

const recaptchaContainer = ref<HTMLElement | null>(null)
const widgetId = ref<number | null>(null)
const loadingError = ref(false)
const isLoading = ref(true)
const isTimeout = ref(false)
let timeoutTimer: number | null = null

// Methods
const loadRecaptchaScript = (): Promise<void> => {
  return new Promise<void>((resolve, reject) => {
    if (window.grecaptcha) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = `https://www.google.com/recaptcha/api.js?hl=${locale.value}&render=explicit`
    script.async = true
    script.defer = true

    script.onload = () => resolve()
    script.onerror = error => reject(error)

    document.head.appendChild(script)
  })
}

const renderRecaptcha = async () => {
  await nextTick()
  if (!window.grecaptcha || !recaptchaContainer.value) { return }

  if (widgetId.value !== null) {
    window.grecaptcha.reset(widgetId.value)
    return
  }

  widgetId.value = window.grecaptcha.render(recaptchaContainer.value, {
    'sitekey': props.siteKey,
    'theme': props.theme,
    'size': props.size,
    'tabindex': props.tabindex,
    'callback': (response: string) => emit('verify', response),
    'expired-callback': () => emit('expired'),
    'error-callback': () => emit('error'),
  })
}

const resetRecaptcha = () => {
  if (window.grecaptcha && widgetId.value !== null) {
    window.grecaptcha.reset(widgetId.value)
  }
}

const executeRecaptcha = () => {
  if (window.grecaptcha && widgetId.value !== null) {
    return window.grecaptcha.getResponse(widgetId.value)
  }
  return ''
}

const startTimeoutTimer = () => {
  // Clear any existing timer
  if (timeoutTimer !== null) {
    window.clearTimeout(timeoutTimer)
  }

  // Set new 30-second timeout
  timeoutTimer = window.setTimeout(() => {
    if (isLoading.value) {
      isLoading.value = false
      isTimeout.value = true
    }
  }, 30000)
}

const clearTimeoutTimer = () => {
  if (timeoutTimer !== null) {
    window.clearTimeout(timeoutTimer)
    timeoutTimer = null
  }
}

const retryLoading = async () => {
  try {
    isLoading.value = true
    isTimeout.value = false
    loadingError.value = false

    // Start the timeout timer
    startTimeoutTimer()

    await loadRecaptchaScript()

    // Wait for grecaptcha to be fully initialized
    const checkGrecaptchaReady = setInterval(() => {
      if (window.grecaptcha && typeof window.grecaptcha.render === 'function') {
        clearInterval(checkGrecaptchaReady)
        renderRecaptcha()
        isLoading.value = false
        clearTimeoutTimer()
      }
    }, 100)
  }
  catch (error) {
    console.error('Failed to load reCAPTCHA on retry:', error)
    isLoading.value = false
    loadingError.value = true
    clearTimeoutTimer()
    emit('error')
  }
}

// Lifecycle
onMounted(async () => {
  try {
    isLoading.value = true

    // Start the timeout timer
    startTimeoutTimer()

    await loadRecaptchaScript()

    // Wait for grecaptcha to be fully initialized
    const checkGrecaptchaReady = setInterval(() => {
      if (window.grecaptcha && typeof window.grecaptcha.render === 'function') {
        clearInterval(checkGrecaptchaReady)
        renderRecaptcha()
        isLoading.value = false
        clearTimeoutTimer()
      }
    }, 100)
  }
  catch (error) {
    console.error('Failed to load reCAPTCHA:', error)
    isLoading.value = false
    loadingError.value = true
    clearTimeoutTimer()
    emit('error')
  }
})

onUnmounted(() => {
  // Remove the widget if it exists
  if (window.grecaptcha && widgetId.value !== null) {
    const container = recaptchaContainer.value
    if (container) {
      container.innerHTML = ''
    }
    widgetId.value = null
  }

  // Clear the timeout timer
  clearTimeoutTimer()
})

// Watch for locale changes
watch(locale, () => {
  // Re-render reCAPTCHA when locale changes
  if (widgetId.value !== null) {
    widgetId.value = null
    if (recaptchaContainer.value) {
      recaptchaContainer.value.innerHTML = ''
    }
    loadingError.value = false // Reset error state on locale change
    isTimeout.value = false // Reset timeout state
    isLoading.value = true // Set loading state

    // Start the timeout timer
    startTimeoutTimer()

    loadRecaptchaScript().then(async () => {
      await nextTick()
      renderRecaptcha()
      isLoading.value = false
      clearTimeoutTimer()
    }).catch((error) => {
      console.error('Failed to load reCAPTCHA after locale change:', error)
      isLoading.value = false
      loadingError.value = true
      clearTimeoutTimer()
      emit('error')
    })
  }
})

// Expose methods to parent component
defineExpose({
  reset: resetRecaptcha,
  execute: executeRecaptcha,
})
</script>

<template>
  <div class="recaptcha-wrap">
    <div v-if="isLoading" class="recaptcha-loading">
      <div class="loading-spinner" />
      <p>Loading Google reCAPTCHA...</p>
    </div>
    <div v-else-if="isTimeout" class="recaptcha-timeout">
      <p>Loading Google reCAPTCHA timed out after 30 seconds.</p>
      <button class="retry-button" @click="retryLoading">
        Retry
      </button>
    </div>
    <div v-else-if="loadingError" class="recaptcha-error">
      <p>Failed to load Google reCAPTCHA. Please check your internet connection and try again.</p>
      <button class="retry-button" @click="retryLoading">
        Retry
      </button>
    </div>
    <div ref="recaptchaContainer" :class="$attrs.class" />
  </div>
</template>

<style scoped>
.recaptcha-wrap {
  max-width: 100%;
}

.recaptcha-error {
  border: 1px solid #f44336;
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  margin: 10px 0;
}

.recaptcha-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: var(--color-white);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.recaptcha-timeout {
  border: 1px solid #ff9800;
  background-color: #fff3e0;
  color: #e65100;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  margin: 10px 0;
}

.retry-button {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.3s;
}

.retry-button:hover {
  background-color: #3367d6;
}
</style>
