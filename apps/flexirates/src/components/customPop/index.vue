<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
})

const op = ref()

const toggle = (event: any) => {
  op.value.toggle(event)
}
const hide = (event: any) => {
  op.value.hide(event)
}
defineExpose({ toggle, hide })
</script>

<template>
  <div>
    <Popover ref="op" class="custom-flexirates-popover">
      <div class=" px-2 py-4">
        <div v-if="props.title" class="popover-header flex justify-between items-center border-b border-[#545454] pb-2">
          <div class="font-semibold text-xl text-[#031f73]">
            {{ props.title }}
          </div>
          <div>
            <i class="pi-times pi cursor-pointer" style="color: #0073cf; font-weight: 700; font-size: 16px;" @click="hide" />
          </div>
        </div>
        <div class="popover-content">
          <slot name="content" />
        </div>
      </div>
    </Popover>
  </div>
</template>

<style lang="scss">
.custom-flexirates-popover.p-popover {
    --p-popover-border-color: #f5f5ff;
    --p-popover-background: #f5f5ff;

    .popover-content {
        color: var(--colors-grey);
    }
}
</style>
