<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'
import { getThemeDisplayInfo } from '@/utils/theme'

const layoutStore = useLayoutStore()
const { themeMode, isDarkTheme } = storeToRefs(layoutStore)

const toggleTheme = () => {
  layoutStore.toggleDarkTheme()
}

const themeDisplayInfo = computed(() => {
  return getThemeDisplayInfo(themeMode.value, isDarkTheme.value)
})

const themeIcon = computed(() => {
  return themeDisplayInfo.value.icon
})

const themeLabel = computed(() => {
  return `${themeDisplayInfo.value.label} - ${themeDisplayInfo.value.description}`
})
</script>

<template>
  <Button
    :icon="themeIcon"
    :aria-label="themeLabel"
    :title="themeLabel"
    severity="secondary"
    text
    rounded
    class="theme-toggle-button"
    @click="toggleTheme"
  />
</template>

<style lang="scss" scoped>
.theme-toggle-button {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }

  :deep(.pi) {
    font-size: 1.2rem;
  }
}
</style>
