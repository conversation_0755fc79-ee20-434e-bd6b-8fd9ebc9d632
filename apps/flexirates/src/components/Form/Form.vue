<script lang="ts" setup>
import type { FormContext, FormExpose, FormProps } from './types'
import { provide, reactive, toRefs, watch } from 'vue'
import { formProvideKey } from '.'

const props = withDefaults(defineProps<FormProps>(), {
  labelWidth: '',
  labelPosition: 'left',
  showError: true,
  validateFirst: false,
})

const emit = defineEmits<{
  (e: 'submit', values: Record<string, any>): void
  (e: 'failed', errors: Record<string, string[]>): void
}>()

// 初始化表单数据，使用model或空对象
const formData = reactive<Record<string, any>>(props.model ? { ...props.model } : {})
const errors = reactive<Record<string, string[]>>({})
const fields = reactive<Record<string, FormContext>>({})

// 注册表单项
const registerField = (field: FormContext) => {
  fields[field.name] = field
  if (field.value !== undefined) {
    formData[field.name] = field.value
  }
}

// 移除表单项
const unregisterField = (name: string) => {
  delete fields[name]
  delete formData[name]
  delete errors[name]
}

// 设置表单项的值
const setFieldValue = (name: string, value: any) => {
  if (fields[name]) {
    formData[name] = value
    validateField(name)
  }
}

// 验证单个字段
const validateField = (name: string) => {
  const field = fields[name]
  if (!field || !field.rules) { return true }

  const rules = Array.isArray(field.rules) ? field.rules : [field.rules]
  errors[name] = []

  for (const rule of rules) {
    try {
      if (typeof rule === 'function') {
        rule(formData[name], formData)
      }
      else {
        if (rule.required && !formData[name]) {
          throw new Error(rule.message || 'Field is required')
        }
        if (rule.validator) {
          rule.validator(formData[name], formData)
        }
      }
    }
    catch (e: any) {
      errors[name].push(e.message)
      if (props.validateFirst) { break }
    }
  }

  return errors[name].length === 0
}

// 验证整个表单
const validate = (callback?: (isValid: boolean, errors: Record<string, string[]>) => void) => {
  const results = Object.keys(fields).map(name => validateField(name))
  const isValid = results.every(valid => valid)
  if (callback) {
    callback(isValid, errors)
  }
  return isValid
}

// 重置表单
const reset = () => {
  Object.keys(fields).forEach((name) => {
    formData[name] = undefined
    errors[name] = []
  })
}

// 提交表单
const handleSubmit = () => {
  const valid = validate()
  if (valid) {
    emit('submit', { ...formData })
  }
  else {
    emit('failed', { ...errors })
  }
}

// 监听model prop的变化
const { model } = toRefs(props)

// 监听model的变化，更新表单数据
watch(() => model?.value, (newVal) => {
  if (newVal) {
    // 更新表单数据
    Object.keys(newVal).forEach((key) => {
      formData[key] = newVal[key]
    })
  }
}, { deep: true })

// 监听formData的变化，同步回model
watch(formData, (val) => {
  const currentModel = model?.value
  if (currentModel) {
    Object.keys(val).forEach((key) => {
      if (currentModel[key] !== val[key]) {
        currentModel[key] = val[key]
      }
    })
  }
}, { deep: true })

// 提供上下文
provide(formProvideKey, {
  model: formData,
  errors,
  registerField,
  unregisterField,
  validateField,
  setFieldValue,
  labelWidth: props.labelWidth,
  labelPosition: props.labelPosition,
  showError: props.showError,
})

// 暴露方法
defineExpose<FormExpose>({
  validate,
  reset,
  validateField,
})
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <slot />
  </form>
</template>
