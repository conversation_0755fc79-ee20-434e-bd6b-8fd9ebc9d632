import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '@/store/modules/user'

// 白名单路由，不需要登录即可访问
const whitelist = [
  'flexiratesLogin',
  'flexiratesRegister',
]

export const flexiratesGuard = async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  const isWhiteList = whitelist.includes(to.name as string)

  const userStore = useUserStore()

  if (isWhiteList) {
    // 如果用户已登录，并且访问的是登录页面，则重定向到首页
    if (userStore.isLoggedIn && to.name === 'flexiratesLogin') {
      return next({ path: '/' })
    }
    return next()
  }
  else {
    if (!userStore.isLoggedIn && from.name !== 'flexiratesLogin') {
      const redirect = to?.path || '/'

      if (redirect === '/' || redirect === '/home/<USER>') {
        return next({ name: 'flexiratesLogin', query: { ...to.query } })
      }

      return next({
        name: 'flexiratesLogin',
        query: {
          redirect,
          ...to.query,
        },
      })
    }
    else if (userStore.isLoggedIn && userStore.user === null) {
      await userStore.initializeFromStorage()
      return next({ path: to.path, query: to.query })
    }
    else {
      return next()
    }
  }
}
