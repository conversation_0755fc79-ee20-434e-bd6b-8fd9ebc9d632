import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { publicRouterName } from './publicRouterName'

// 白名单路由，不需要登录即可访问
const whitelist = [
  publicRouterName.LOGIN,
  publicRouterName.REGISTER,
] as const

export const flexiratesGuard = async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  const isWhiteList = whitelist.includes(to.name as typeof whitelist[number])

  const userStore = useUserStore()

  if (isWhiteList) {
    // 如果用户已登录，并且访问的是登录页面，则重定向到首页
    if (userStore.isLoggedIn && to.name === publicRouterName.LOGIN) {
      return next({ path: '/' })
    }
    return next()
  }
  else {
    if (!userStore.isLoggedIn && from.name !== publicRouterName.LOGIN) {
      const redirect = to?.path || '/'

      if (redirect === '/' || redirect === '/home/<USER>') {
        return next({ name: publicRouterName.LOGIN, query: { ...to.query } })
      }

      return next({
        name: publicRouterName.LOGIN,
        query: {
          redirect,
          ...to.query,
        },
      })
    }
    else if (userStore.isLoggedIn && userStore.user === null) {
      await userStore.initializeFromStorage()
      return next({ path: to.path, query: to.query })
    }
    else {
      return next()
    }
  }
}
