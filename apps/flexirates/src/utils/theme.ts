/**
 * 检测系统是否为暗黑主题
 * @returns {boolean} 系统是否为暗黑主题
 */
export const detectSystemTheme = (): boolean => {
  if (typeof window === 'undefined') { return false }
  return window.matchMedia('(prefers-color-scheme: dark)').matches
}

/**
 * 获取初始主题模式
 * 优先级：localStorage > 默认跟随系统
 * @returns {Theme.ThemeMode} 初始主题模式
 */
export const getInitialThemeMode = (): Theme.ThemeMode => {
  if (typeof window === 'undefined') { return 'system' }

  const stored = localStorage.getItem('layout')
  if (stored) {
    try {
      const parsed = JSON.parse(stored)
      // 检查是否有新的 themeMode 字段
      if (['light', 'dark', 'system'].includes(parsed.themeMode)) {
        return parsed.themeMode as Theme.ThemeMode
      }
      // 向后兼容：检查旧的 isDarkTheme 字段
      if (typeof parsed.isDarkTheme === 'boolean') {
        return parsed.isDarkTheme ? 'dark' : 'light'
      }
    }
    catch (e) {
      console.warn('Failed to parse theme from localStorage:', e)
    }
  }

  return 'light'
}

/**
 * 根据主题模式解析实际的暗黑状态
 * @param {Theme.ThemeMode} mode 主题模式
 * @returns {boolean} 是否为暗黑主题
 */
export const resolveThemeMode = (mode: Theme.ThemeMode): boolean => {
  switch (mode) {
    case 'light':
      return false
    case 'dark':
      return true
    case 'system':
      return detectSystemTheme()
    default:
      return false
  }
}

/**
 * 获取下一个主题模式（用于循环切换）
 * @param {ThemeMode} currentMode 当前主题模式
 * @returns {ThemeMode} 下一个主题模式
 */
export const getNextThemeMode = (currentMode: Theme.ThemeMode): Theme.ThemeMode => {
  const modes: Theme.ThemeMode[] = ['light', 'dark', 'system']
  const currentIndex = modes.indexOf(currentMode)
  const nextIndex = (currentIndex + 1) % modes.length
  return modes[nextIndex]
}

/**
 * 获取主题模式的显示信息
 * @param {Theme.ThemeMode} mode 主题模式
 * @param {boolean} isDark 当前实际暗黑状态（用于 system 模式显示）
 * @returns {object} 包含图标和标签的对象
 */
export const getThemeDisplayInfo = (mode: Theme.ThemeMode, isDark: boolean = false) => {
  switch (mode) {
    case 'light':
      return {
        icon: 'pi pi-sun',
        label: 'Light Mode',
        description: 'Click for Dark Mode',
      }
    case 'dark':
      return {
        icon: 'pi pi-moon',
        label: 'Dark Mode',
        description: 'Click for System Mode',
      }
    case 'system':
      return {
        icon: 'pi pi-desktop',
        label: 'System Mode',
        description: `Currently ${isDark ? 'Dark' : 'Light'}, Click for Light Mode`,
      }
    default:
      return {
        icon: 'pi pi-sun',
        label: 'Unknown',
        description: 'Theme Toggle',
      }
  }
}

/**
 * 创建系统主题变化监听器
 * @param {Function} callback 主题变化回调函数
 * @returns {Function} 清理函数
 */
export const createSystemThemeListener = (callback: (isDark: boolean) => void) => {
  if (typeof window === 'undefined') {
    return () => {} // 服务端渲染时返回空函数
  }

  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleChange = (e: MediaQueryListEvent) => {
    callback(e.matches)
  }

  mediaQuery.addEventListener('change', handleChange)

  // 返回清理函数
  return () => {
    mediaQuery.removeEventListener('change', handleChange)
  }
}
