/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddCardOrBank: typeof import('./../components/payment/addCardOrBank.vue')['default']
    Avatar: typeof import('primevue/avatar')['default']
    BaseCardType: typeof import('./../components/common/BaseCardType.vue')['default']
    BaseDataTable: typeof import('./../components/common/BaseDataTable.vue')['default']
    BaseDataTableActions: typeof import('./../components/common/BaseDataTableActions.vue')['default']
    BaseDataTableSearch: typeof import('./../components/common/BaseDataTableSearch.vue')['default']
    BaseDataTableSearchItem: typeof import('./../components/common/BaseDataTableSearchItem.vue')['default']
    BaseDataTableSuperSearch: typeof import('./../components/common/BaseDataTableSuperSearch.vue')['default']
    BaseExportDialog: typeof import('./../components/common/BaseExportDialog.vue')['default']
    BaseFileUpload: typeof import('./../components/common/BaseFileUpload.vue')['default']
    BaseLoadingWrap: typeof import('./../components/common/BaseLoadingWrap.vue')['default']
    BasePopover: typeof import('./../components/common/BasePopover.vue')['default']
    BaseRangeDatePicker: typeof import('./../components/common/BaseRangeDatePicker.vue')['default']
    BaseSearch: typeof import('./../components/common/BaseSearch.vue')['default']
    BaseSearchItem: typeof import('./../components/common/BaseSearchItem.vue')['default']
    BaseTag: typeof import('./../components/common/BaseTag.vue')['default']
    Button: typeof import('primevue/button')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    CollapsePanel: typeof import('./../components/collapsePanel.vue')['default']
    Column: typeof import('primevue/column')['default']
    ConfirmDialog: typeof import('primevue/confirmdialog')['default']
    CookiesPop: typeof import('./../components/cookiesPop/index.vue')['default']
    CustomDialog: typeof import('./../components/customDialog/index.vue')['default']
    CustomMessage: typeof import('./../components/customMessage/index.vue')['default']
    CustomPop: typeof import('./../components/customPop/index.vue')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divide: typeof import('./../components/divide/index.vue')['default']
    Divider: typeof import('primevue/divider')['default']
    Drawer: typeof import('primevue/drawer')['default']
    EnhancedDatePicker: typeof import('./../components/common/EnhancedDatePicker.vue')['default']
    Form: typeof import('./../components/Form/Form.vue')['default']
    FormField: typeof import('@primevue/forms/formfield')['default']
    FormItem: typeof import('./../components/Form/FormItem.vue')['default']
    GlobalSetup: typeof import('./../components/globalSetup/index.vue')['default']
    GoogleRecaptchaV2: typeof import('./../components/googleRecaptchaV2/index.vue')['default']
    Image: typeof import('primevue/image')['default']
    InputMask: typeof import('primevue/inputmask')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputNumberRange: typeof import('./../components/InputNumberRange/index.vue')['default']
    InputRange: typeof import('./../components/InputRange/index.vue')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Message: typeof import('primevue/message')['default']
    OverlayBadge: typeof import('primevue/overlaybadge')['default']
    Password: typeof import('primevue/password')['default']
    Popover: typeof import('primevue/popover')['default']
    ProgressBar: typeof import('primevue/progressbar')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RadioButton: typeof import('primevue/radiobutton')['default']
    RadioButtonGroup: typeof import('primevue/radiobuttongroup')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScheduleEditDialog: typeof import('./../components/scheduleDialog/ScheduleEditDialog.vue')['default']
    ScrollPanel: typeof import('primevue/scrollpanel')['default']
    Select: typeof import('primevue/select')['default']
    TermsAndConditions: typeof import('./../components/termsAndConditions/index.vue')['default']
    ThemeToggle: typeof import('./../components/common/ThemeToggle.vue')['default']
    TieredMenu: typeof import('primevue/tieredmenu')['default']
    Toast: typeof import('primevue/toast')['default']
  }
}
