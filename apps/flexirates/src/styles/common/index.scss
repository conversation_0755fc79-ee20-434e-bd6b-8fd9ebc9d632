html {
    font-size: 14px;
    --colors-primary: #181349;
    --colors-primary-1: #1B1548;
    --colors-menu-active: #ebb700;
    --bg-colors-white: #f5f5ff;
    --sidebar-bg: var(--color-white);
    --header-bg: var(--color-white);
    --body-bg: var(--bg-colors-white);
    --text-primary: var(--p-primary-300);
    --surface-hover: var(--p-surface-200);
    --p-progressspinner-color-1: var(--p-primary-300);
    --menu-item-color: rgb(84, 84, 84);
    --swiper-pagination-color: var(--color-blue-500);
    --p-textarea-focus-border-color: var(--color-blue-500);
    --p-inputtext-focus-border-color: var(--color-blue-500);
    --p-select-focus-border-color: var(--color-blue-500);
    --p-select-overlay-background: var(--color-white);
    --p-radiobutton-checked-border-color: var(--color-blue-500);
    --p-radiobutton-checked-background: var(--color-blue-500);
    --p-radiobutton-checked-hover-border-colo: var(--color-blue-500);
    --p-radiobutton-checked-hover-background: var(--color-blue-500);
    --p-radiobutton-border-color: var(--color-blue-500);
    --p-radiobutton-checked-hover-border-color: var(--color-blue-500);
    --p-radiobutton-focus-ring-color: var(--color-blue-500);
    --p-inputtext-border-color: var(--color-gray-500);
    --p-textarea-border-color: var(--color-gray-500);
    --p-radiobutton-border-color: var(--color-gray-500);
    --p-select-border-color: var(--color-gray-500);
    --p-multiselect-border-color: var(--color-gray-500);
    --p-multiselect-overlay-background: var(--color-white);
    --p-inputgroup-addon-border-color:var(--color-gray-500);
    color: var(--colors-primary);
    --border-radius: 16px;
}

body {
    margin: 0;
    font-family: 'Jost', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.p-select-overlay {
    --p-select-overlay-border-radius: 8px;
    --p-select-overlay-border-color: transparent;
}

.p-select-list {
    background-color: transparent;
    --p-select-option-border-radius: 0;
    --p-select-option-selected-background: transparent;
    --p-select-list-padding: 8px 16px;
    border-radius: 8px;
    --p-select-option-padding: 0.75rem;

    .p-select-option {
        border-bottom: 1px solid var(--color-gray-500);

        &:last-child {
            border-bottom: none;
        }

        &.p-select-option-selected {
            text-decoration: underline;
            font-weight: 600;
        }
    }
}

/* 暗黑模式 CSS 变量 */
html.dark {
    --colors-primary: #4f46e5;
    --colors-primary-1: #6366f1;
    --color-blue-500: #3b82f6;
    --color-orange-500: #f59e0b;
    --color-white: #1f2937;
    --color-gray-500: #9ca3af;
    --colors-menu-active: #fbbf24;
    --bg-colors-white: #111827;
    --sidebar-bg: var(--color-white);
    --header-bg: var(--color-white);
    --body-bg: var(--bg-colors-white);
    --text-primary: #f9fafb;
    --surface-hover: #374151;
    --menu-item-color: #d1d5db;
    --p-select-overlay-background: var(--color-white);
    --p-multiselect-overlay-background: var(--color-white);
}