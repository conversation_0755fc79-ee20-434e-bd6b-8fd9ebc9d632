<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import { storeToRefs } from 'pinia'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import { publicRouterName } from '@/router/publicRouterName'
import { property as propertyApi } from '@/services/flexirates/index'
import { useFlexiratesRegisterStore } from '@/store/modules/register'
import { useUserStore } from '@/store/modules/user'

const emits = defineEmits<{
  (e: 'changeShowType', type: 'login' | 'register' | 'forgetPassword'): void
}>()

const router = useRouter()

const userStore = useUserStore()

const registerStore = useFlexiratesRegisterStore()

const { model } = storeToRefs(registerStore)

const formRef = ref<FormContext | null>(null)

const loading = ref(false)

const schema = toTypedSchema(yup.object({
  firstName: yup.string().required('First Name is required'),
  lastName: yup.string().required('Last Name is required'),
  email: yup.string().required('Email is required').email('Email is invalid'),
  mobile: yup.string().required('Mobile is required'),
  property_number: yup.string().required('Property Number is required'),
  verification_code: yup.string().required('Verification Code is required'),
  password: yup.string()
    .required('Password required')
    .min(8, 'At least 8 characters')
    .test('lowercase', 'Must include a lowercase letter', v => /[a-z]/.test(v || ''))
    .test('uppercase', 'Must include an uppercase letter', v => /[A-Z]/.test(v || ''))
    .test('number', 'Must include a number', v => /\d/.test(v || ''))
    .test('symbol', 'Must include a symbol', v => /[!@#$%^&*(),.?":{}|<>]/.test(v || '')),
}))

const onFormSubmit = async () => {
  const result = await formRef.value?.validate()

  if (result?.valid) {
    try {
      loading.value = true
      const { data, code } = await propertyApi.getPropertyList({
        property_number: model.value.property_number,
        verification_code: model.value.verification_code,
      })

      if (code === 0) {
        userStore.setRegisterAccountInfo(data)
        router.push({
          name: publicRouterName.REGISTER,
        })
      }
    }
    finally {
      loading.value = false
    }
  }
}
</script>

<template>
  <div class="register-form-wrap">
    <div class="register-form-content">
      <h1 class="title !mt-0">
        Welcome
      </h1>

      <VeeForm ref="formRef" :validation-schema="schema" class="register-form" @submit="onFormSubmit">
        <div class="flex flex-col gap-4 lg:flex-row">
          <Field
            v-slot="{ field, errorMessage }" v-model="model.first_name" as="div"
            class="flex flex-col lg:gap-2 flex-1" name="firstName"
          >
            <label for="firstName">
              First Name <span class="text-red-500">*</span>
            </label>
            <InputText v-bind="field" class="form-input" name="firstName" type="text" />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <Field
            v-slot="{ field, errorMessage }" v-model="model.last_name" as="div"
            class="flex flex-col lg:gap-2 flex-1" name="lastName"
          >
            <label for="lastName">
              Last Name <span class="text-red-500">*</span>
            </label>
            <InputText v-bind="field" class="form-input" type="text" />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <div class="flex flex-col gap-4 lg:flex-row">
          <Field
            v-slot="{ field, errorMessage }" v-model="model.mobile_phone" as="div"
            class="flex flex-col lg:gap-2 flex-1" name="mobile"
          >
            <label for="mobile">
              Mobile <span class="text-red-500">*</span>
            </label>
            <InputText v-bind="field" class="form-input" name="mobile" type="text" />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
          <Field
            v-slot="{ field, errorMessage }" v-model="model.email" as="div" class="flex flex-col lg:gap-2 flex-1"
            name="email"
          >
            <label for="email">
              Email <span class="text-red-500">*</span>
            </label>
            <InputText
              v-bind="field" class="form-input" type="text" :input-props="{
                autocomplete: 'new-password',
              }"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <div class="flex flex-col gap-4 lg:flex-row">
          <Field
            v-slot="{ field, errorMessage, handleChange }" v-model="model.password" as="div"
            class="flex flex-col lg:gap-2 flex-1" name="password"
          >
            <label for="password">
              Password <span class="text-red-500">*</span>
            </label>
            <Password
              v-model="field.value" :feedback="false" toggle-mask class="form-input w-full"
              :input-props="{
                autocomplete: 'new-password',
              }"
              @value-change="handleChange"
            />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>

        <div class="mt-2 lg:mt-4">
          <h2 class="tips-title">
            Account Verification
          </h2>
          <div class="sub-title">
            This information can be found on your rate notice.
          </div>
        </div>

        <div class="flex flex-col gap-4 lg:flex-row">
          <Field
            v-slot="{ field, errorMessage }" v-model="model.property_number" as="div"
            class="flex flex-col lg:gap-2 flex-1" name="property_number"
          >
            <label for="propertyNumber">
              Property Number <span class="text-red-500">*</span>
            </label>
            <InputText v-bind="field" class="form-input" name="property_number" type="text" />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
            <div class="property-number-tips">
              You will find your property number (ten digit number) in the top right corner on the front
              page of your rates notice.
            </div>
          </Field>
          <Field
            v-slot="{ field, errorMessage }" v-model="model.verification_code" as="div"
            class="flex flex-col lg:gap-2 flex-1" name="verification_code"
          >
            <label for="verificationCode">
              Verification Code <span class="text-red-500">*</span>
            </label>
            <InputText v-bind="field" class="form-input" type="text" />
            <Message v-if="errorMessage" severity="error" variant="simple">
              {{ errorMessage }}
            </Message>
          </Field>
        </div>
      </VeeForm>
    </div>

    <div class="form-tools">
      <div class="form-tools-item">
        <Button
          class="register-submit w-full" :loading="loading" severity="warn" label="REGISTER"
          @click="onFormSubmit"
        />
      </div>
      <div class="flex justify-center">
        <Button severity="info" text label="Back to Login" @click="emits('changeShowType', 'login')" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.register-form-wrap {
  display: flex;
  justify-content: center;
  background: var(--color-white);
  padding: 30px 30px 0;
  padding-bottom: 110px;
  border-radius: 24px;
  width: 800px;
  min-width: 400px;
  min-height: 650px;
  position: relative;
  z-index: 2;

  @include media-breakpoint-down(lg) {
    min-width: 400px;
  }

  @include media-breakpoint-down(md) {
    width: 100%;
    min-width: 100%;
    padding: 0 20px;
  }

  label {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-gray-500);
  }

  .register-form-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .register-form {
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 1rem;
    gap: 1.3rem;

    @include media-breakpoint-down(md) {
      gap: 0.25rem;
    }
  }

  .title {
    margin-top: 0;
    margin-bottom: 0.25rem;
    font-size: 54px;
    font-weight: 800;
  }

  .subtitle {
    font-size: 18px;
    margin-bottom: 2rem;
    color: var(--color-gray-500);

    @include media-breakpoint-down(md) {
      margin-bottom: 1rem;
    }

    .subtitle-account {
      color: #0073CF;

    }
  }

  .register-submit {
    margin-top: 2rem;
    border-radius: 14px;
    font-size: 14px;
    height: 50px;
    --p-button-padding-y: 16px;
    --p-button-padding-x: 24px;
    --p-button-border-color: var(--p-gray-800);
  }

  .recaptcha-title {
    font-weight: 600;
    color: var(--color-gray-500);
  }

  .form-input {
    font-size: 16px;
    font-weight: 600;
  }

  .form-input {
    :deep(.p-inputtext) {
      font-size: 16px;
      font-weight: 600;
      flex: 1;
      width: 100%;
    }
  }

  .tips-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--color-gray-500);

    @include media-breakpoint-down(md) {
      font-size: 20px;
    }
  }

  .sub-title {
    margin-top: 0.5rem;
    font-size: 16px;
    color: var(--color-gray-500);

    @include media-breakpoint-down(md) {
      font-size: 14px;
    }
  }

  .property-number-tips {
    font-size: 10px;
    color: var(--color-gray-500);
  }
}
</style>
