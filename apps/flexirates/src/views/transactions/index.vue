<script setup lang="ts">
import type { DictItem } from '@/services/api/dict'
import { Format } from '@shared'
import dayjs from 'dayjs'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import CustomDialog from '@/components/customDialog/index.vue'
import { useDict } from '@/composables/useDict'
import { useExport } from '@/composables/useExport'
import { useRequestList } from '@/composables/useRequestList'
import { SearchFieldType } from '@/constants/search'
import { transactions as transactionsApi } from '@/services/flexirates'
import { formatDate } from '@/utils/date'
import { addAllToDict } from '@/utils/dict'
import Search from './components/search.vue'
import transactionTable from './components/transactionTable.vue'

defineOptions({
  name: 'FlexiratesTransactionsList',
})

const route = useRoute()

const requestList = useRequestList({
  requestFn: transactionsApi.getList,
})
const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  setSearchParams,
} = requestList

// 列配置
const columns = ref<TableColumnItem[]>([
  {
    field: 'status',
    header: 'Status',
    style: { minWidth: '110px' },
    template: 'status',
  },
  {
    field: 'property_name',
    header: 'Property',
    style: { minWidth: '110px' },
    template: 'property_name',
  },
  {
    field: 'address',
    header: 'Address',
    style: { minWidth: '150px' },
    template: 'address',
  },
  {
    field: 'created_at',
    header: 'Date',
    style: { minWidth: '110px' },
    template: 'date',
    sortable: true,
    sortField: 'created_at',
  },
  {
    field: 'payment_method',
    header: 'Payment Method',
    style: { minWidth: '110px' },
    template: 'payment_method',
  },
  {
    field: 'amount',
    header: 'Amount',
    style: { width: '140px' },
    template: 'amount',
  },
  {
    field: 'action',
    header: '',
    style: { width: '140px' },
    template: 'action',
    isCustom: true,
  },

])

const searchModel = ref<any>({
  'property_ids': null,
  'status': null,
  'created_at[]': [],
})
const searchFields = computed(() => [
  {
    name: 'property_ids',
    label: 'Property',
    type: SearchFieldType.MULTISELECT,
    options: propertyOptions.value,
    placeholder: 'Please select property',
    defaultValue: [],
    width: '380px',
  },
  {
    name: 'status',
    label: 'Status',
    type: SearchFieldType.SELECT,
    placeholder: 'All',
    options: statusOptions.value,
    defaultValue: '',
  },
  {
    name: 'created_at[]',
    label: 'Date range',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Please select date range',
    defaultValue: [],
  },

])
const amountTypeOptions = ref<DictItem[]>([])

const propertyOptions = ref<any[]>([])

const statusOptions = ref<DictItem[]>([])

const paymentMethodOptions = ref<any[]>([])

useDict('trans_amount_range', (res) => {
  amountTypeOptions.value = res
})

const { getLabel: getTransStatusLabel } = useDict('trans_status', (res) => {
  statusOptions.value = addAllToDict(res, { label: 'All', value: null })
})

const moreSearchFields = computed(() => [
  {
    name: 'amount_range',
    label: 'Amount',
    type: SearchFieldType.SELECT,
    placeholder: 'Please select amount range.',
    options: amountTypeOptions.value,
    defaultValue: null,
  },
  {
    name: 'payment_method_ids',
    label: 'Payment Method',
    type: SearchFieldType.MULTISELECT,
    placeholder: 'Please select payment method.',
    options: paymentMethodOptions.value,
    maxLabels: 5,
    defaultValue: [],
  },
])

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

// 手动刷新数据
const refreshData = () => {
  refresh()
}

const handleSort = (event: any) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const getStatusIcon = (status: number) => {
  switch (status) {
    case 1:
      return 'pi-verified'
    case 2:
      return 'pi-times-circle'
    default:
      return 'pi-hourglass'
  }
}
const getColor = (status: number) => {
  switch (status) {
    case 1:
      return '#39b54a'
    case 2:
      return '#eb001b'
    default:
      return '#545454'
  }
}

const getPaymentLabel = (value: number) => {
  return paymentMethodOptions.value.find(item => item.value === value)?.label
}
// const getPropertyLabel = (id: number) => {
//   return propertyOptions.value.find(item => item.value === id)?.label
// }

const detailVisible = ref(false)
const detailData = ref<any>({})
const detailLoading = ref(false)
const openDialog = async (id: number) => {
  try {
    detailLoading.value = true
    const res = await transactionsApi.getTransactionDetail(id)
    detailData.value = res.data
  }
  catch (error) {
    console.log(error)
  }
  finally {
    detailLoading.value = false
  }
  detailVisible.value = true
}

const exportPopRef = ref()
const exportForm = ref({
  type: 'csv',
  schedule: 'all',
})
const exportToggle = (event: Event) => {
  exportPopRef.value.toggle(event)
}

const confirmVisible = ref(false)

const confirmExport = (event: Event) => {
  exportPopRef.value.hide(event)
  confirmVisible.value = true
}
const { isExporting, handleExport } = useExport({
  exportFn: transactionsApi.exportTransactionsList,
  getParams: () => {
    const params = {
      export_cols: [
        'status',
        'property_name',
        'date',
        'amount',
        'payment_method',
      ],
      export_type: exportForm.value.type,
    }
    if (exportForm.value.schedule === 'all') {
      return setSearchParams({
        ...searchModel.value,
        ...params,
      })
    }
    else {
      const formatParams = { ...searchModel.value }
      for (const key in formatParams) {
        if (key === 'created_at[]') {
          if (Array.isArray(formatParams[key]) && formatParams[key].length === 2) {
            const dateArray = formatParams[key]
            const startDate = dayjs(dateArray[0]).format('YYYY-MM-DD')
            const endDate = dateArray?.[1] ? dayjs(dateArray[1]).format('YYYY-MM-DD') : startDate
            formatParams[key] = [startDate, endDate]
          }
        }
        if (formatParams[key] === null || formatParams[key] === undefined || formatParams[key] === '') {
          delete formatParams[key]
        }
        if (Array.isArray(formatParams[key]) && formatParams[key]?.length === 0) {
          delete formatParams[key]
        }
      }
      Object.assign(params, formatParams)
      return params
    }
  },
  onExportStart: () => {
    window.$toast.add({
      severity: 'info',
      summary: 'Export Started',
      detail: 'Preparing your export file...',
      life: 3000,
    })
  },
  onExportSuccess: () => {
    confirmVisible.value = false
  },
})

onMounted(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    searchModel.value = {
      property_ids: query.id ? [Number(query.id)] : [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      'property_ids': [],
      'status': '',
      'created_at[]': [],
    }
    setSearchParams(searchModel.value)
    refresh()
  }
  Promise.all([
    transactionsApi.getAllProperty({ is_all: 1 }).then((res) => {
      propertyOptions.value = res.data.map(item => ({
        label: item.street_address,
        value: item.id,
      }))
    }),
    transactionsApi.getAllAccount().then((res) => {
      paymentMethodOptions.value = res.data.map(item => ({
        label: item.payment_method,
        value: item.id,
      }))
    }),
  ])
})
</script>

<template>
  <div>
    <div class=" bg-white dark:bg-gray-800 transaction-header rounded-2xl flex  items-center mb-6">
      <div class="text-3xl font-bold">
        Transactions
      </div>
    </div>
    <div class="transactions-content">
      <div>
        <Search
          v-model="searchModel" :loading="loading" :basic-search-fields="searchFields"
          :advanced-search-fields="moreSearchFields" class="invoice-list-search" @search="handleSearch"
        />
      </div>
      <div class=" bg-white p-4 w-full  rounded-lg">
        <transactionTable
          class="flexirates-transactions-table" :value="list" :columns="columns"
          :show-edit-column="false" :show-search-bar="false" :scrollable="true" :show-multiple-column="false"
          :loading="loading" :paginator="false" :rows="50" :total-records="total" :lazy="true" data-key="id"
          sort-mode="single" search-placeholder="Search" type-placeholder="Filter By" :failed="failed"
          :failure-message="failureMessage" :striped-rows="false" @page="handlePageChange" @sort="handleSort"
          @refresh="refreshData"
        >
          <template #status="{ data }">
            <div class="flex items-center gap-2" :style="{ color: getColor(data.status) }">
              <i class="pi" :class="getStatusIcon(data.status)" />
              <span class="underline font-medium">{{ getTransStatusLabel(data.status) }}</span>
            </div>
          </template>
          <template #property_name="{ data }">
            <span class="underline" :class="{ 'text-[#eb001b]': data.status === 2 }">
              {{ data?.property_name }}
            </span>
          </template>
          <template #address="{ data }">
            <span class="underline" :class="{ 'text-[#eb001b]': data.status === 2 }">
              {{ data.property_address }}
            </span>
          </template>
          <template #payment_method="{ data }">
            <span :class="{ 'text-[#eb001b]': data.status === 2 }">
              {{ getPaymentLabel(data.payment_method_id) }}
            </span>
          </template>
          <template #date="{ data }">
            <span :class="{ 'text-[#eb001b]': data.status === 2 }">
              {{ dayjs(data.created_at).format('DD MMM YYYY') }}
            </span>
          </template>
          <template #amount="{ data }">
            <span :class="{ 'text-[#eb001b]': data.status === 2 }">
              {{ data.payment_currency }} {{ Format.formatAmount(data?.payment_amount) }}
            </span>
          </template>
          <template #action="{ data }">
            <Button
              severity="secondary" variant="text" label="View Details" class="underline"
              :style="{ color: data.status === 2 ? '#eb001b' : '' }" @click="openDialog(data.id)"
            />
          </template>
          <template #actionHeader>
            <div class="flex justify-center w-full">
              <Button label="EXPORT" class="btn" @click="exportToggle($event)" />
            </div>
          </template>
        </transactionTable>
      </div>
    </div>
    <div class="dialog">
      <CustomDialog
        :visible="detailVisible" title="Transaction Details"
        @update:visible="(val) => (detailVisible = val)"
      >
        <template #content>
          <div class="detail-content">
            <div class="detail-content-item">
              <div class="detail-content-label">
                Status:
              </div>
              <div class="detail-content-text font-semibold" :style="{ color: getColor(detailData?.status) }">
                {{
                  getTransStatusLabel(detailData?.status) }}
              </div>
            </div>
            <div class="detail-content-item">
              <div class="detail-content-label">
                Amount:
              </div>
              <div class="detail-content-text font-semibold">
                {{ `${detailData?.payment_currency} ${Format.formatAmount(detailData?.payment_amount)}` }}
              </div>
            </div>
            <div class="detail-content-item">
              <div class="detail-content-label">
                Date:
              </div>
              <div class="detail-content-text font-semibold">
                {{ formatDate(detailData?.created_at) }}
              </div>
            </div>
            <!-- <div class="detail-content-item">
              <div class="detail-content-label">
                Account Number:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.account_no }}
              </div>
            </div> -->
            <!-- <div class="detail-content-item">
              <div class="detail-content-label">
                Account Name:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.account_name }}
              </div>
            </div> -->
            <!-- <div class="detail-content-item">
              <div class="detail-content-label">
                BSB:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.bsb }}
              </div>
            </div> -->
            <div class="detail-content-item">
              <div class="detail-content-label">
                Payment Method:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.payment_method }}
              </div>
            </div>
            <div class="detail-content-item">
              <div class="detail-content-label">
                Nickname:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.property_name }}
              </div>
            </div>
            <div class="detail-content-item">
              <div class="detail-content-label">
                Property Address:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.property_address }}
              </div>
            </div>
            <!-- <div class="detail-content-item">
              <div class="detail-content-label">
                Payment Suburb:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.property_suburb }}
              </div>
            </div> -->
            <div class="detail-content-item">
              <div class="detail-content-label">
                Postcode:
              </div>
              <div class="detail-content-text font-semibold">
                {{ detailData?.property_postcode }}
              </div>
            </div>
          </div>
        </template>
      </CustomDialog>
      <CustomDialog :visible="confirmVisible" @update:visible="(val) => (confirmVisible = val)">
        <template #content>
          <div class="w-58">
            <div class="text-center text-[#031f73] text-2xl font-semibold">
              Are you sure you want to export
              {{ exportForm.schedule === 'all' ? 'all transactions' : 'this current selection' }}?
            </div>
            <div class="mt-8 flex flex-col gap-3">
              <Button
                label="YES" severity="warn" class="w-full" :loading="isExporting"
                @click="handleExport(exportForm.type)"
              />
              <Button label="CANCEL" class="w-full" @click="confirmVisible = false" />
            </div>
          </div>
        </template>
      </CustomDialog>
    </div>
    <div class="pop">
      <CustomPop ref="exportPopRef" title="Export Options">
        <template #content>
          <div class="flex justify-between gap-10">
            <div class="flex flex-col gap-4">
              <div class="font-semibold text-xl">
                File Type
              </div>
              <div class="flex flex-col gap-2">
                <RadioButtonGroup v-model="exportForm.type" name="type" class="flex flex-col gap-4">
                  <div class="flex items-center gap-2">
                    <RadioButton input-id="csv" value="csv" />
                    <label for="csv">.csv</label>
                  </div>

                  <!-- <div class="flex items-center gap-2">
                    <RadioButton input-id="xls" value="xls" />
                    <label for="mushroom">.xls</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <RadioButton input-id="pdf" value="pdf" />
                    <label for="pepper">.pdf</label>
                  </div> -->
                </RadioButtonGroup>
                <!-- <Message v-if="$form.ingredient?.invalid" severity="error" size="small" variant="simple">{{
                  $form.ingredient.error?.message }}</Message> -->
              </div>
            </div>
            <div class="flex flex-col gap-4">
              <div class="font-semibold text-xl">
                Schedules
              </div>
              <div class="flex flex-col gap-2">
                <RadioButtonGroup v-model="exportForm.schedule" name="schedule" class="flex flex-col gap-4">
                  <div class="flex items-center gap-2">
                    <RadioButton input-id="all" value="all" />
                    <label for="cheese">All</label>
                  </div>
                  <!-- <div class="flex items-center gap-2">
                    <RadioButton input-id="current" value="current" />
                    <label for="mushroom">Current<br> Filter</label>
                  </div> -->
                </RadioButtonGroup>
                <!-- <Message v-if="$form.ingredient?.invalid" severity="error" size="small" variant="simple">{{
                  $form.ingredient.error?.message }}</Message> -->
              </div>
            </div>
          </div>
          <div class="mt-6">
            <Button label="EXPORT" severity="warn" class="btn w-full" @click="confirmExport($event)" />
          </div>
        </template>
      </CustomPop>
    </div>
  </div>
</template>

<style>
.flexirates-transactions-table.p-datatable{
  .p-datatable-table-container {

    .p-datatable-tbody {
      tr {
        td {
          border-bottom: 1px solid #545454;
        }
        &:last-child {
          td {
            border-bottom: none;
          }
        }
      }
    }
  }
}
</style>

<style scoped lang="scss">
.transaction-header {
  padding: 24px 26px;
  font-size: 26px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  padding: 1.5rem 0;
  width: 400px;
  gap: 20px;

  &-item {
    display: flex;
    align-items: center;
    flex: 1;
  }

  &-label {
    font-size: 16px;
    font-weight: 600;
    width: 170px;
  }
}

.btn {
  display: block;
  padding: 6px 30px;
}
</style>
