<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import dayjs from 'dayjs'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import * as yup from 'yup'
import CustomDialog from '@/components/customDialog/index.vue'
import { useDict } from '@/composables/useDict'
import router from '@/router'
import { paymentMethod as paymentMethodApi } from '@/services/flexirates'

const toast = useToast()

const route = useRoute()

const bankDetail = ref()
const nickname = ref('')
const isEditLoading = ref(false)
const deleteisEditLoading = ref(false)

const { getLabel: getStatusLabel } = useDict('payment_method_status')
const { getLabel: getWeightLabel } = useDict('weight')
const getDetail = async () => {
  try {
    const res = await paymentMethodApi.getBankOrCardDetail({ id: Number(route?.params.id) })
    bankDetail.value = res.data
    nickname.value = (res.data as unknown as { nickname: string }).nickname
  }
  catch (error) {
    console.log(error)
  }
}

const formatDate = (date: Date) => {
  return dayjs(date).format('DD MMM YYYY')
}

const addNickName = async () => {
  if (!nickname.value) { return }
  try {
    isEditLoading.value = true
    const res = await paymentMethodApi.changeNickName({
      id: Number(route?.params.id),
      nickname: nickname.value,
    })
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully added Nickname .', life: 3000 })
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isEditLoading.value = false
  }
}

const confirmationVisible = ref(false)
const noticeVisible = ref(false)
const editVisible = ref(false)
const deletePaymentMethod = async () => {
  try {
    deleteisEditLoading.value = true
    const res = await paymentMethodApi.deletePaymentMethod({
      id: Number(route?.params.id),
    })
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully delete card .', life: 3000 })

      router.push({ name: 'paymentMethods' })
    }
    else {
      confirmationVisible.value = false
      noticeVisible.value = true
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    deleteisEditLoading.value = false
  }
}

const formRef = ref()
const editisEditLoading = ref(false)
const editBankForm = ref({
  bsb: '',
  accountName: '',
  accountNumber: '',
})

const schema = toTypedSchema(yup.object({
  bsb: yup.string().required('BSB is required').max(6, 'Maximum input of 6 digits.'),
  accountName: yup.string().required('Account Name is required'),
  accountNumber: yup.string().required('Account Number is required'),
}))
const onFormSubmit = async (values: Record<string, any>) => {
  const sendData = {
    id: bankDetail.value.id,
    bsb: values.bsb,
    account_name: values.accountName,
    account_no: values.accountNumber,
  }
  try {
    editisEditLoading.value = true
    const res = await paymentMethodApi.editBank(sendData)
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully Update Bank Info.', life: 3000 })
      editVisible.value = false
      getDetail()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    editisEditLoading.value = false
  }
}

onMounted(() => {
  getDetail()
})
</script>

<template>
  <div>
    <div v-if="bankDetail" class="card-header">
      <div class="card-title">
        {{ bankDetail?.account_name }}
        <span class="status-indicator" :class="{ 'status-inactive-indicator': bankDetail.status !== 1 }">{{
          getStatusLabel(bankDetail.status) }}</span>
      </div>
      <div class="flex items-center gap-6">
        <Button label="EDIT DETAILS" class="btn" severity="warn" @click="editVisible = true" />
        <Button label="DELETE ACCOUNT" class="btn" severity="warn" @click="confirmationVisible = true" />
      </div>
    </div>

    <div v-if="bankDetail" class="details">
      <h2 class="section-title">
        Details
      </h2>
      <div class="divider" />

      <div class="details-grid pb-8">
        <div class="detail-item">
          <div class="detail-label">
            Bank Account Name:
          </div>
          <div class="detail-value">
            {{ bankDetail.account_name }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">
            Payment Method Added Date:
          </div>
          <div class="detail-value">
            {{ formatDate(bankDetail.created_at) }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">
            BSB:
          </div>
          <div class="detail-value">
            {{ bankDetail.bsb }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">
            Last Payment Date:
          </div>
          <div class="detail-value">
            {{ formatDate(bankDetail.last_payment) }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">
            Account Number:
          </div>
          <div class="detail-value">
            {{ bankDetail?.account_no }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">
            Edit count:
          </div>
          <div class="detail-value">
            {{ bankDetail?.edit_times }}
          </div>
        </div>
      </div>

      <h2 class="section-title-description">
        Linked properties
      </h2>
      <div class="payment-methods">
        <div class="section-description">
          Payment Preferences
        </div>
        <div class="divider" />

        <div v-for="(item, index) in bankDetail.customer_plan" :key="item.id" class="payment-method-row">
          <div class="serial">
            {{ index + 1 }}
          </div>
          <div class="address">
            {{ item.street_address }}
          </div>
          <div class="payment-type" :class="item.weight === 1 ? 'primary' : 'secondary'">
            {{ getWeightLabel(item.weight) }} Payment Method
          </div>
        </div>
      </div>

      <div class="nickname-section">
        <h2 class="section-title">
          Add a Nickname
        </h2>
        <div class="divider" />

        <div class="nickname-form">
          <div class="w-60">
            NickName
          </div>
          <input v-model="nickname" type="text" class="nickname-input" placeholder="Nickname">
          <Button class="save-button" severity="warn" :loading="isEditLoading" label="SAVE" @click="addNickName" />
        </div>
        <div class="help-text">
          Give this payment method a name to help you recognize it.
        </div>
      </div>
    </div>

    <div class="dialog">
      <CustomDialog :visible="confirmationVisible" @update:visible="(val) => (confirmationVisible = val)">
        <template #content>
          <div class="w-[180px] ">
            <div class="font-bold text-2xl text-center text-[#031f73]">
              Are you sure you want to delete this Payment Method?
            </div>
            <div class="flex flex-col gap-y-4 mt-8">
              <Button class="btn" label="CANCEL" @click="confirmationVisible = false" />
              <Button class="btn" label="YES" severity="warn" :loading="deleteisEditLoading" @click="deletePaymentMethod" />
            </div>
          </div>
        </template>
      </CustomDialog>
      <CustomDialog :visible="noticeVisible" title="Notice" @update:visible="(val) => (noticeVisible = val)">
        <template #content>
          <div class="w-[660px] line-height-20">
            <div class="font-semibold text-xl pd-10">
              This Payment Method can not be deleted.
            </div>
            <div class="pd-10">
              It is currently linked to the following properties :
            </div>
            <div class="py-[10px] pl-[20px]">
              <p v-for="(item, index) in bankDetail?.customer_plan" :key="item.id">
                {{ index + 1 }}. {{ item.street_address }} ( {{ getWeightLabel(item.weight) }} )
              </p>
            </div>
            <div class="pd-10">
              To remove this payment method, please update the payment method assigned to each property listed above.
            </div>
            <div class="font-semibold pd-10">
              How to update:
            </div>
            <div class="pd-10">
              <p>
                Go to the <span class="font-semibold">Property Details</span> of each linked property and click "Change
                Payment Method."
              </p>
              <p>
                You can only choose from <span class="font-semibold">existing payment methods</span>, so if you don’t
                have an alternative saved, please add a new one first via the Payment Methods section.
              </p>
            </div>
          </div>
        </template>
      </CustomDialog>
      <CustomDialog
        :visible="editVisible" title="Edit Bank Account Details"
        @update:visible="(val) => (editVisible = val)"
      >
        <template #content>
          <div class="w-[550px]">
            <VeeForm ref="formRef" :validation-schema="schema" @submit="onFormSubmit">
              <Field
                v-slot="{ field, errorMessage }" v-model="editBankForm.bsb" class="edit-form-item" as="div"
                name="bsb"
              >
                <div class="relative flex items-center">
                  <label class="edit-label">BSB Number<span class="required">*</span>:</label>
                  <InputText v-bind="field" type="text" class="w-full" />
                  <Message v-if="errorMessage" class="mt-2 edit-message" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, errorMessage }" v-model="editBankForm.accountName" class="edit-form-item" as="div"
                name="accountName"
              >
                <div class="relative flex items-center">
                  <label class="edit-label">Account Name<span class="required">*</span>:</label>
                  <InputText v-bind="field" type="text" class="w-full" />
                  <Message v-if="errorMessage" class="mt-2 edit-message" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, errorMessage }" v-model="editBankForm.accountNumber" class="edit-form-item"
                as="div" name="accountNumber"
              >
                <div class="relative flex items-center">
                  <label class="edit-label">Account Number<span class="required">*</span>:</label>
                  <InputText v-bind="field" type="text" class="w-full" />
                  <Message v-if="errorMessage" class="mt-2 edit-message" severity="error" variant="simple">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <div class="tips-text">
                Please note: You can only update your bank account details up to 3 times for security reasons.
                Current Edit Count : {{ bankDetail?.edit_times }}/3
              </div>
              <div class="flex justify-end pt-6">
                <Button label="SAVE" severity="warn" type="submit" class="btn save-btn" :loading="editisEditLoading" />
              </div>
            </VeeForm>
          </div>
        </template>
      </CustomDialog>
    </div>
  </div>
</template>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background-color: #ffffff;
  border-radius: 16px;
  margin-bottom: 20px;

  .card-title {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #1a1a2e;
  }

  .status-indicator {
    background-color: #f5f5ff;
    color: #39B54A;
    padding: 10px 30px;
    border-radius: 8px;
    font-size: 12px;
    margin-left: 35px;
  }

}

.btn {
  padding: 8px 16px;
  font-size: 16px;
}

.save-btn {
  padding: 8px 30px;
}

.required {
  color: #0073cf;
  font-weight: 600;
}

.tips-text {
  color: #EB001B;
}

.edit-form-item {
  margin: 25px 0;

  .edit-label {
    width: 200px;
    font-weight: 600;
  }

  .edit-message {
    position: absolute;
    top: 30px;
    left: 150px;
  }
}

.details {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 30px;
  color: #545454;

  .section-title,
  .section-title-description {
    font-size: 18px;

    margin: 0 0 15px;
    font-weight: bold;
  }

  .section-title-description {
    margin-bottom: 6px;
  }

  .divider {
    height: 1px;
    background-color: #545454;
    margin: 10px 0;
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 30px 0 20px 0;

  }

  .detail-item {
    display: flex;
    font-size: 14px;
  }

  .detail-label {
    font-weight: 600;
    width: 220px;
  }

  .payment-method-row {
    display: grid;
    width: 60%;
    gap: 50px;
    grid-template-columns:50px 1fr 1fr;
    padding: 15px 0;

    .address {
      width: auto;
    }
  }

  .payment-type {
    font-size: 14px;
    width: 180px;
    font-weight: 600;
  }

  .primary {
    color: #39b54a;
  }

  .nickname-section {
    margin-top: 40px;

  }

  .nickname-form {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 40px;
  }

  .nickname-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 14px;
  }

  .save-button {
    padding: 10px 25px;
    font-weight: 600;
  }

  .help-text {
    color: #6b7280;
    font-size: 12px;
    margin-top: 5px;
    margin-left: 225px;
  }
}

// 弹窗样式
.line-height-20 {
  line-height: 20px;

  .pd-10 {
    padding: 10px 0;
  }

  .pd-left-40 {
    padding-left: 40px;

  }
}
</style>
