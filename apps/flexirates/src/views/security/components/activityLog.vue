<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import type { DictItem } from '@/services/api/dict'
import dayjs from 'dayjs'
import { computed, ref } from 'vue'
import { useDict } from '@/composables/useDict'
import { useRequestList } from '@/composables/useRequestList'
import { security as securityApi } from '@/services/flexirates'
import { addAllToDict } from '@/utils/dict'

interface Emits {
  (e: 'update:params', value: string | null): void
}

const emit = defineEmits<Emits>()

const requestList = useRequestList<Api.UserActivityLogListData, Api.UserActivityLogListReq>({
  requestFn: securityApi.getActivityLog,
})

const columns = ref<TableColumnItem[]>([
  {
    field: '',
    header: 'Date & Time',
    style: { width: '200px', padding: '0 8px' },
    template: 'date',
  },
  {
    field: 'activity_type_desc',
    header: 'Activity Type',
    style: { width: '300px' },
  },
  {
    field: 'activity_detail',
    header: 'Details',
  },
  {
    field: 'status',
    header: 'Status',
    style: { width: '100px' },
    template: 'status',
  },
])

const {
  list,
  loading,
  total,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

const activeIndex = ref<any>(null)
const tagsOptions = ref<DictItem[]>([])

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 重置数据
const resetList = () => {
  currentPage.value = 1
  requestList.refresh()
}

// 加载更多
const loadingMore = ref(false)
const moreList = ref<any>([])
const loadMore = async () => {
  if (loadingMore.value) { return }
  loadingMore.value = true
  try {
    const nextPage = currentPage.value + 1
    const res = await securityApi.getActivityLog({
      page: nextPage,
      page_size: pageSize.value,
      activity_types: activeIndex.value,
    })

    if (Array.isArray(res?.data?.data) && res.data.data.length > 0) {
      moreList.value = res.data.data
      currentPage.value = nextPage
    }
  }
  finally {
    loadingMore.value = false
  }
}

// 监听筛选变化，重置数据
const onTagClick = (val: any) => {
  activeIndex.value = val
  currentPage.value = 1
  const params = { page: 1, page_size: pageSize.value, activity_types: val }
  requestList.updateParams(params)
  emit('update:params', val)
}

// 初始化tags
useDict('customer_activity_type_filter', (res) => {
  tagsOptions.value = addAllToDict(res, { label: 'ALL ACTIVITIES', value: null })
})

// 初始化加载
resetList()
const combinedList = computed(() => {
  return [...list.value, ...moreList.value]
})
</script>

<template>
  <div>
    <div class="search-tags">
      <div
        v-for="tag in tagsOptions" :key="tag.label" class="tag-item cursor-pointer"
        :class="{ active: activeIndex === tag.value }" @click="onTagClick(tag.value)"
      >
        {{ tag.label }}
      </div>
    </div>
    <BaseDataTable
      :value="combinedList" :columns="columns" :loading="loading" :show-multiple-column="false"
      :paginator="false" :show-search-bar="false" :rows="10" :lazy="true" data-key="id" :failed="failed"
      :failure-message="failureMessage" :row-hover="false" :total-records="total" class="mt-4" @refresh="resetList"
      @page="(e: DataTablePageEvent) => handlePageChange(e)"
    >
      <template #date="{ data }">
        {{ dayjs(data?.activity_at).format('DD MMM YYYY, HH:mm A') }}
      </template>
      <template #status="{ data }">
        <span
          class="font-semibold" :class="{
            'text-green-500': data?.activity_status === 1,
            'text-red-500': data?.activity_status === 0,
          }"
        >
          {{ data?.activity_status_desc }}
        </span>
      </template>
      <template #empty-action>
        <Button label="Refresh" />
      </template>
    </BaseDataTable>
    <div v-if="combinedList?.length < total" class="flex justify-center mt-4">
      <Button label="VIEW MORE" class="btn" :loading="loadingMore" @click="loadMore" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  padding-top: 2rem;

  .tag-item {
    border: 2px solid var(--color-gray-500);
    padding: 5px 15px;
    border-radius: 8px;

    &.active {
      background-color: var(--color-tag-active);
      color: var(--color-white);
    }
  }
}

.btn {
  padding: .5rem 2rem;
}
</style>
