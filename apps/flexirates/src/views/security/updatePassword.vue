<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import { user as userApi } from '@/services/flexirates'

const toast = useToast()
const router = useRouter()

const model = ref({
  currentPassword: '',
  newPassword: '',
  confirmNewPassword: '',
})
const formRef = ref()
const loading = ref(false)

const schema = toTypedSchema(yup.object({
  currentPassword: yup.string().required('Please enter your current password'),
  newPassword: yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/\d/, 'Password must contain at least one number'),
  confirmNewPassword: yup.string()
    .oneOf([yup.ref('newPassword')], 'Passwords do not match')
    .required('Please confirm your new password'),

}))

const onFormSubmit = async (value: Record<string, any>) => {
  try {
    loading.value = true
    const sendData = {
      password: value.currentPassword,
      new_password: value.newPassword,
    }
    console.log(sendData)
    const res = await userApi.updatePersonalInfo(sendData)
    if (res.code === 0) {
      toast.add({ severity: 'success', summary: 'Tips', detail: 'Successfully update password .', life: 3000 })
      router.back()
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="flexirates-wrap">
    <div class="flexirates-title">
      <div class="flexirates-title-text">
        Security
      </div>
    </div>
    <div class="security-container">
      <div class="border-b-1 border-[#545454] text-xl font-semibold py-8">
        Update Password
      </div>
      <VeeForm ref="formRef" class="flex flex-col gap-16" :validation-schema="schema" @submit="onFormSubmit">
        <Field
          v-slot="{ field, errorMessage }" v-model="model.currentPassword" as="div"
          class="flex flex-col gap-4 mt-10" name="currentPassword"
        >
          <div class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10">
            <label for="currentPassword" class="w-60">Current Password</label>
            <div class="relative">
              <Password
                v-bind="field" id="user-current-password" toggle-mask fluid :feedback="false"
                class="form-input lg:w-120 xl:w-160" name="securityCurrentPassword" type="password"
                autocomplete="change-current-password"
              />
              <Message v-if="errorMessage" variant="text" class="text-red-500 absolute top-13 left-0 w-auto">
                {{ errorMessage }}
              </Message>
            </div>
          </div>
        </Field>
        <Field
          v-slot="{ field, errorMessage }" v-model="model.newPassword" as="div" class="flex flex-col gap-4"
          name="newPassword"
        >
          <div class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10">
            <label for="newPassword" class="w-60">New Password</label>
            <div class="flex flex-col xl:flex-row gap-2">
              <div class="relative">
                <Password
                  v-bind="field" toggle-mask fluid :feedback="false" class="form-input lg:w-120 xl:w-160" name="newPassword"
                  type="password"
                />
                <Message v-if="errorMessage" variant="text" class="text-red-500 absolute top-13 left-0 w-auto">
                  {{ errorMessage }}
                </Message>
              </div>
              <div class="xl:ml-8">
                Include at least one capital letter,one number,and at least 8 characters
              </div>
            </div>
          </div>
        </Field>
        <Field
          v-slot="{ field, errorMessage }" v-model="model.confirmNewPassword" as="div" class="flex flex-col gap-4"
          name="confirmNewPassword"
        >
          <div class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10">
            <label for="confirmNewPassword" class="w-60">Confirm Password</label>
            <div class="relative">
              <Password
                v-bind="field" toggle-mask fluid :feedback="false" class="form-input lg:w-120 xl:w-160"
                name="confirmPassword" type="password"
              />

              <Message v-if="errorMessage" variant="text" class="text-red-500 absolute top-13 left-0 w-auto">
                {{ errorMessage }}
              </Message>
            </div>
          </div>
        </Field>
        <div class="flex justify-center items-center py-10 gap-10">
          <Button label="CANCEL" severity="secondary" class="btn cancel-button" @click="$router.back()" />
          <Button label="UPDATE" severity="warn" :loading="loading" class="btn" type="submit" />
        </div>
      </VeeForm>
    </div>
  </div>
</template>

<style scoped>
.btn {
  display: inline-block;
  padding: 10px 50px;
}

.security-container {
  color: #545454;
}

.cancel-button {
  background-color: transparent !important;
  color: var(--color-gray-500);
  --p-button-secondary-border-color: #1E1E1E;
  --p-button-secondary-hover-border-color: #1E1E1E;

  &:hover {
    color: var(--color-gray-500);
  }
}
</style>
