<script setup lang="ts">
import { MDialog } from '@ui'
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { ref, watch } from 'vue'
import * as yup from 'yup'
import AddCardOrBank from '@/components/payment/addCardOrBank.vue'
import { home as homeApi, transactions as transactionsApi } from '@/services/flexirates'

interface Props {
  propertyId?: number | null
}

const props = defineProps<Props>()

const emit = defineEmits(['success'])

const modelValue = defineModel<boolean>('modelValue', { required: true, default: false, type: Boolean })

const onceOffLoading = ref(false)
const onceOffFormRef = ref<InstanceType<typeof VeeForm>>()
const showAddPaymentDialog = ref(false)

const isDisabledProperty = ref(!!props.propertyId)

const onceOffForm = ref({
  property: props.propertyId || null,
  paymentMethod: null,
  amount: null,
})

// 验证规则
const onceOffSchema = toTypedSchema(yup.object({
  property: yup.number()
    .required('Property method is required'),
  amount: yup.number().required('Amount is required'),
  paymentMethod: yup
    .mixed<number | string>()
    .test(
      'is-valid-payment-method',
      'Payment method is required',
      (value) => {
        if (typeof value === 'number' && !Number.isNaN(value)) {
          return true
        }
        if (typeof value === 'string' && value.trim() !== '') {
          return true
        }
        return false
      },
    )
    .required('Payment method is required'),
}))

// 选项数据
const isPropertyLoading = ref(false)
const propertyOptions = ref<{ label: string, value: number }[]>([])
const isPaymentMethodLoading = ref(false)
const paymentMethodOptions = ref<{ label: string, value: number | string }[]>([])

// 获取属性列表
const getAllPropertyList = async () => {
  isPropertyLoading.value = true
  try {
    const res = await transactionsApi.getAllProperty()
    propertyOptions.value = res.data.map(item => ({
      label: item.street_address ?? '',
      value: item.id ?? 0,
    }))
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isPropertyLoading.value = false
  }
}

// 获取支付方法列表
const getAllPaymentMethodList = async () => {
  isPaymentMethodLoading.value = true
  try {
    const res = await transactionsApi.getAllAccount()
    paymentMethodOptions.value = res.data.map((item) => {
      return {
        label: item.payment_method,
        value: item.id,
      }
    })
    paymentMethodOptions.value.push({ label: 'Add New Payment Method', value: '' })
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isPaymentMethodLoading.value = false
  }
}

// 处理支付方法选择
const handleSelectChange = (event: number | string) => {
  if (typeof event === 'string' && event === '') {
    showAddPaymentDialog.value = true
  }
}

// 处理添加支付方法对话框关闭
const handleDialogClose = () => {
  showAddPaymentDialog.value = false
  getAllPaymentMethodList()
}

// 提交表单
const onOnceOffSubmit = async (values: any) => {
  try {
    onceOffLoading.value = true
    const sendData = {
      property_id: values.property,
      payment_method_id: values.paymentMethod,
      amount: values.amount,
    }
    const res = await homeApi.onceOffPayment(sendData)
    if (res.code === 0) {
      modelValue.value = false
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'ONE-OFF payment successful',
      })
      emit('success')
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    onceOffLoading.value = false
  }
}

// 监听 visible 变化，初始化数据
watch(() => modelValue.value, (newVal) => {
  if (newVal) {
    getAllPropertyList()
    getAllPaymentMethodList()
    // 如果传入了 propertyId，设置默认值
    console.log('props.propertyId', props.propertyId)

    if (props.propertyId) {
      onceOffForm.value.property = props.propertyId
    }
  }
}, { immediate: true })
</script>

<template>
  <div>
    <MDialog
      v-model="modelValue" header="Make a One-off Payment"
    >
      <div class="w-full md:w-[650px]">
        <VeeForm
          ref="onceOffFormRef" :initial-values="onceOffForm" :validation-schema="onceOffSchema"
          @submit="onOnceOffSubmit"
        >
          <div class="flex flex-col gap-6 pt-2">
            <Field
              v-slot="{ field, errorMessage, handleChange }" v-model="onceOffForm.property" as="div"
              name="property"
            >
              <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-0 relative">
                <div class="font-semibold text-[16px] w-88">
                  Select Property
                </div>
                <Select
                  v-model="field.value" :options="propertyOptions" option-label="label" option-value="value"
                  placeholder="Select a property" fluid size="large" :disabled="isDisabledProperty" :loading="isPropertyLoading" @value-change="handleChange"
                />
                <Message v-if="errorMessage" severity="error" variant="simple" class="absolute top-[38px] left-60">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>
            <Field
              v-slot="{ field, errorMessage, handleChange }" v-model="onceOffForm.paymentMethod" as="div"
              name="paymentMethod"
            >
              <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-0 relative">
                <div class="font-semibold text-[16px] w-88">
                  Payment Method
                </div>
                <Select
                  v-model="field.value" :options="paymentMethodOptions" option-label="label"
                  option-value="value" class="w-full" placeholder="Select or add payment method" fluid size="large"
                  :loading="isPaymentMethodLoading"
                  @value-change="
                    (e: any) => {
                      handleChange(e)
                      handleSelectChange(e)
                    }"
                >
                  <template #option="slotProps">
                    <div class="flex items-center">
                      <div v-if="slotProps.option.value">
                        {{ slotProps.option.label }}
                      </div>
                      <div v-else>
                        {{ slotProps.option.label }}
                      </div>
                    </div>
                  </template>
                </Select>
                <Message v-if="errorMessage" severity="error" variant="simple" class="absolute top-[38px] left-60">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>
            <Field v-slot="{ field, errorMessage, handleChange }" v-model="onceOffForm.amount" as="div" name="amount">
              <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-0 relative">
                <div class="font-semibold text-[16px] w-88">
                  Amount
                </div>
                <InputNumber
                  v-model="field.value" placeholder="Enter amount" fluid size="large" mode="currency"
                  currency="AUD" locale="en-AU" @value-change="handleChange"
                />
                <Message v-if="errorMessage" severity="error" variant="simple" class="absolute top-[38px] left-60">
                  {{ errorMessage }}
                </Message>
              </div>
            </Field>

            <div class="once-off-tips">
              <div>
                Important:
              </div>
              <p>
                Making a one-off payment will not affect your current property payment schedule.Once the payment
                is settled, it will appear in your Transactions List, and your total remaining payments will be
                reduced accordingly.
              </p>
            </div>
            <div class="flex justify-end mt-2">
              <Button label="CONFIRM" severity="warn" class="confirm-btn" type="submit" :loading="onceOffLoading" />
            </div>
          </div>
        </VeeForm>
      </div>
    </MDialog>

    <AddCardOrBank v-if="showAddPaymentDialog" v-model:visible="showAddPaymentDialog" :show-radio="true" @close="handleDialogClose" />
  </div>
</template>

<style scoped lang="scss">
.once-off-tips {
  color: #eb001b;
  border: 2px solid #0073cf;
  padding: 1rem;

  div {
    font-size: 18px;
    margin-bottom: 1.5rem;
    font-weight: 600;
  }
}

.confirm-btn {
  font-size: 16px;
  padding: 8px 20px;
  --p-button-label-font-weight: 600;
}
</style>
