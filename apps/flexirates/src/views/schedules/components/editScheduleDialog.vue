<script setup lang="ts">
import { onMounted, ref } from 'vue'
import ScheduleEditDialog from '@/components/scheduleDialog/ScheduleEditDialog.vue'
import { transactions as transactionsApi } from '@/services/flexirates'

interface Props {
  visible: boolean
  propertyId?: number | null
}

const props = defineProps<Props>()
const emit = defineEmits(['update:visible', 'confirm'])

const editScheduleDialogRef = ref<InstanceType<typeof ScheduleEditDialog>>()

const propertyList = ref<{ label: string, value: string }[]>([])

// 获取属性列表
const getAllPropertyList = async () => {
  try {
    const res = await transactionsApi.getAllProperty()
    propertyList.value = res.data.map(item => ({
      label: item.street_address ?? '',
      value: item.id?.toString() ?? '',
    }))
  }
  catch (error) {
    console.log('Failed to fetch property list:', error)
  }
}

// 处理确认事件
const handleConfirm = () => {
  emit('confirm')
}

// 处理弹窗显示/隐藏
const handleUpdateVisible = (val: boolean) => {
  emit('update:visible', val)
}

// 组件挂载时获取属性列表
onMounted(() => {
  getAllPropertyList()
  if (props.propertyId) {
    editScheduleDialogRef.value?.setPropertyDetailAndLoad(String(props.propertyId))
  }
})
</script>

<template>
  <ScheduleEditDialog
    ref="editScheduleDialogRef"
    :visible="props.visible"
    :property-list="propertyList"
    @update:visible="handleUpdateVisible"
    @confirm="handleConfirm"
  />
</template>
