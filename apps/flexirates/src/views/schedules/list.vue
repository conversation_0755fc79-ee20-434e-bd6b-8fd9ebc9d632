<script setup lang="ts">
import { Format } from '@shared'
import dayjs from 'dayjs'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { useRequestList } from '@/composables/useRequestList'
import { schedule as scheduleApi } from '@/services/flexirates'
import EditScheduleDialog from './components/editScheduleDialog.vue'
import OneOffPaymentDialog from './components/oneOffPaymentDialog.vue'

defineOptions({
  name: 'schedulesList',
})

const requestList = useRequestList<Api.FlexiratesListItem, Api.FlexiratesGetScheduleReq>({
  requestFn: scheduleApi.getScheduleList,
})

const {
  list,
  loading,
  failed,
  failureMessage,
  other,
  refresh,
} = requestList

useListRefresh('schedulesList', refresh)

const router = useRouter()

const { getLabel: getStatusLabel } = useDict('subscription_status')

const { getLabel: getFrequencyLabel } = useDict('payment_plan_show')

const { getLabel: getCardName } = useDict('credit_brand')

const columns = ref<TableColumnItem[]>([
  {
    field: 'property',
    header: 'Property',
    style: { width: '200px' },
    template: 'property',
  },
  {
    field: 'status',
    header: 'Status',
    template: 'status',
    style: { width: '100px' },
  },
  {
    field: 'frequency',
    header: 'Frequency',
    style: { width: '100px' },
    template: 'frequency',
  },
  {
    field: 'amount',
    header: 'Amount',
    template: 'amount',
    style: { width: '150px' },
  },
  {
    field: 'method',
    header: 'Payment Method',
    style: { width: '150px' },
    template: 'paymentMethod',
  },
  {
    field: 'newDate',
    header: 'New Payment Date',
    template: 'newDate',
    style: { width: '150px' },
  },
  {
    field: 'failed_times',
    header: 'Failed Payment',
    style: { width: '150px' },
    template: 'failedTimes',
  },
  {
    field: 'edited_times',
    header: 'Schedule Edit Count',
    style: { width: '150px' },
    template: 'editCount',
  },
  // {
  //   field: 'actions',
  //   header: '',
  //   style: { width: '120px' },
  //   template: 'actions',
  // },
])

// const listDetailVisible = ref(false)
const detailVisible = ref(false)
// const statusOptions = ref<DictItem[]>([])

// const displayDetailData = ref()

// const popRef = ref()
// const sendType = ref<string[]>([])
// const sendLoading = ref(false)

const onceOffVisible = ref(false)

// const searchModel = ref<Api.FlexiratesGetScheduleReq>({
//   status: null,
// })

const refreshData = () => {
  refresh()
}

const getColor = (status: number) => {
  if (status === 1) {
    return '#39b54a'
  }
  if (status === 5) {
    return '#ebb700'
  }
  if (status === 7) {
    return 'red'
  }
  return '#031f73'
}

const handleConfirm = () => {
  detailVisible.value = false
  refresh()
}

const handleOneOffSuccess = () => {
  refresh()
}

const handleRowClick = ({ data }: { data: Api.FlexiratesListItem }) => {
  router.push({
    name: 'schedulesDetail',
    params: {
      id: data.id,
    },
    query: {
      address: data?.street_address,
      status: data?.status,
      related_id: data?.related_id,
    },
  })
}

// onActivated(() => {
//   refresh()
// })
</script>

<template>
  <div class="flexirates-wrap">
    <div class="flexirates-title">
      <div class="flexirates-title-text">
        Schedules
      </div>
      <div class="flex flex-col md:flex-row gap-2 md:gap-6">
        <Button label="MAKE A ONE-OFF PAYMENT" severity="warn" @click="onceOffVisible = true" />
        <Button label="EDIT SCHEDULE" severity="warn" @click="detailVisible = true" />
      </div>
    </div>
    <div class="w-full md:w-2/5">
      <div class="schedule-overview">
        <div v-for="item in other?.stat" :key="item.status" class="schedule-overview-item">
          <div class="schedule-overview-title text-2xl whitespace-nowrap flex items-center gap-2">
            {{ item.status_text }}
            <BasePopover trigger="hover" placement="right" popper-class="user-setting-popper">
              <template #reference>
                <i class="pi pi-question-circle cursor-pointer" />
              </template>
              <!-- 超出宽度自动换行 -->
              <div
                class="max-w-lg break-normal"
              >
                {{ item.help_text }}
              </div>
            </BasePopover>
          </div>
          <div
            class="schedule-overview-display"
          >
            <div class="schedule-overview-display-amount" :style="{ color: getColor(item.status) }">
              {{ item.count }}
            </div>
            <!-- <div class="flex justify-between items-center schedule-overview-link" @click="handleView(item.status)">
                <div class="cursor-pointer underline">
                  View all
                </div>
                <div><i class="pi pi-chevron-right cursor-pointer" /></div>
              </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 md:px-4 md:w-full rounded-lg overflow-x-auto">
      <BaseDataTable
        :value="list" :columns="columns" :loading="loading" :show-multiple-column="false"
        :paginator="false" :show-search-bar="false" :rows="50" :lazy="true" data-key="id" :failed="failed"
        :failure-message="failureMessage" :row-hover="true" @refresh="refreshData" @row-click="handleRowClick"
      >
        <template #property="{ data }">
          <div class="flex flex-col min-h-10 justify-center">
            <span class="underline font-medium" style="font-size: 15px;">
              {{ data.nickname }}
            </span>
            <span>{{ data.street_address }}</span>
          </div>
        </template>
        <template #amount="{ data }">
          AUD {{ Format.formatAmount(data?.amount) }}
        </template>
        <template #newDate="{ data }">
          {{ dayjs(data.next_process_date).format('DD MMM YYYY') }}
        </template>
        <template #count="{ data }">
          <span :class="{ 'text-red-500': data.status === 7 }">{{ data.count }}</span>
        </template>
        <!-- <template #actions="{ data }">
            <Button text label="View Details" class="underline font-semibold" @click="openDetail(data.id)" />
          </template> -->
        <template #frequency="{ data }">
          {{ getFrequencyLabel(data.process_type) }}
        </template>
        <template #paymentMethod="{ data }">
          <span>
            {{ `${getCardName(data.credit_brand)}(${data.account_no?.slice(-4)})` }}
          </span>
        </template>
        <template #failedTimes="{ data }">
          <span>
            {{ data?.failed_times }} of {{ data?.failable_times }}
          </span>
        </template>
        <template #editCount="{ data }">
          <span>
            {{ data?.edited_times }} of {{ data?.editable_count }}
          </span>
        </template>
        <!-- 自定义空状态 -->
        <template #status="{ data }">
          <div :style="{ color: getColor(data.status) }" class="font-medium">
            <span>{{ getStatusLabel(data.status) }}</span>
          </div>
        </template>
        <template #empty-action>
          <Button label="Refresh" />
        </template>
      </BaseDataTable>
    </div>
    <div class="dialog">
      <!-- <Dialog v-model:visible="listDetailVisible" :modal="true" class="custom-flexirates-dialog"
        :style="{ width: '50rem' }">
        <template #container="{ closeCallback }">
          <div class="px-8 py-6">
            <div class="popover-header flex justify-between items-center border-b border-[#545454] pb-2 gap-20">
              <div class="font-semibold text-2xl text-[#181349]">
                Schedule Detail
              </div>
              <div class="flex items-center gap-6">
                <div class="px-6 bg-[#f5f5ff] rounded-lg py-1 font-semibold "
                  :style="{ color: getDetailStatusColor(displayDetailData?.trans_status) }">
                  {{ getTransStatusLabel(displayDetailData?.trans_status) }}
                </div>
                <i class="pi-times pi cursor-pointer" style="color: #0073cf; font-weight: 700; font-size: 16px;"
                  @click="closeCallback" />
              </div>
            </div>
            <div v-if="displayDetailData?.paymentDetails.length > 0">
              <div v-for="(item, index) in displayDetailData?.paymentDetails" :key="item.id">
                <div class="py-4 text-xl font-semibold">
                  {{ index === 0 ? 'First Attempt' : 'Second Attempt' }}
                </div>
                <div class="flex flex-col gap-4">
                  <div class="flex items-center">
                    <div class="w-46">
                      Property Name:
                    </div>
                    <div>{{ item?.property_name }}</div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-46">
                      Property Address:
                    </div>
                    <div>{{ item?.property_address }}</div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-46">
                      Date:
                    </div>
                    <div>{{ dayjs(item?.date || '2025-06-11').format('DD MMM YYYY') }}</div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-46">
                      Amount:
                    </div>
                    <div>${{ item?.amount }}</div>
                  </div>
                  <div class="flex items-center">
                    <div class="w-46">
                      Payment Method:
                    </div>
                    <div>{{ item?.payment_method }}</div>
                  </div>
                </div>
                <div class="flex justify-end mt-4">
                  <Button label="SEND RECEIPT" severity="warn" @click="displayPop($event, item.id)" />
                </div>
              </div>
            </div>
            <div v-else>
              <div class="text-3xl font-semibold flex justify-center py-10">
                No Data
              </div>
            </div>
          </div>
        </template>
      </Dialog> -->
      <!-- <CustomPop ref="popRef" title="Receipt Options">
        <template #content>
          <div class="py-4 flex flex-col gap-4">
            <div class="flex items-center justify-between gap-12">
              <div class="flex items-center gap-2">
                <Checkbox v-model="sendType" input-id="sms" value="SMS" />
                <label for="sms"> Send Via SMS </label>
              </div>
              <div>
                <InputText disabled :placeholder="userStore.user?.mobile" />
              </div>
            </div>
            <div class="flex items-center justify-between gap-12">
              <div class="flex items-center gap-2">
                <Checkbox v-model="sendType" input-id="Email" value="Email" />
                <label for="Email"> Send Via Email </label>
              </div>
              <div>
                <InputText disabled :placeholder="userStore.user?.email" />
              </div>
            </div>
          </div>
          <div class="flex justify-end">
            <Button label="SEND" severity="warn" :loading="sendLoading" @click="sendReceipt" />
          </div>
        </template>
      </CustomPop> -->
    </div>
    <EditScheduleDialog v-model:visible="detailVisible" @confirm="handleConfirm" />

    <OneOffPaymentDialog v-model="onceOffVisible" @success="handleOneOffSuccess" />
  </div>
</template>

<style scoped lang="scss">
.schedule-overview {
  display: grid;
  grid-template-columns: repeat(4, 150px);
  column-gap: 30px;
  color: #545454;

  &-item {
    margin: 2rem 0;

    &-active {
      background-color: #f5f5ff;
    }
  }

  &-title {
    font-weight: 600;
  }

  &-display {
    margin-top: 1rem;
    padding: 15px;
    background-color: #f5f5ff;

    &-amount {
      font-size: 4rem;
      font-weight: 700;
      color: #1b1548;
    }
  }
}

.btn {
  display: inline-block;
  padding: 10px 30px;
  font-size: 20px;
}

:deep(.p-datatable-tbody) {
  >tr {
    cursor: pointer;

    &:hover {
      background-color: var(--bg-colors-white);
    }
  }
}
</style>
