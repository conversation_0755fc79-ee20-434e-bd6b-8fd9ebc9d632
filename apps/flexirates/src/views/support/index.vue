<script setup lang="ts">
import { Loader } from '@googlemaps/js-api-loader'
import { onMounted, ref } from 'vue'
import { support as supportApi } from '@/services/flexirates'

const mapRef = ref<any>(null)

const notify = ref()

const initMap = async () => {
  const loader = new Loader({
    apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
    version: 'weekly',
    libraries: ['places', 'marker'],
  })

  try {
    await loader.importLibrary('maps').then(({ Map }) => {
      mapRef.value = new Map(document.getElementById('map') as HTMLElement, {
        center: { lat: -38.06577967561586, lng: 145.4138108815013 },
        zoom: 15,
      })
    })

    // await loader.importLibrary('marker').then((markerLib) => {
    //   const { AdvancedMarkerElement } = markerLib
    //   void new AdvancedMarkerElement({
    //     map: mapRef.value,
    //     position: { lat: -38.06577967561586, lng: 145.4138108815013 },
    //     title: 'Bill Buddy',
    //   })
    // })
  }
  catch (error) {
    console.error('加载Google Maps失败:', error)
  }
}

onMounted(() => {
  Promise.all([
    supportApi.getNotify().then((res) => {
      notify.value = res.data
    }),
    initMap(),
  ])
})
</script>

<template>
  <div class="flexirates-wrap">
    <div class="flexirates-title">
      <div class="flexirates-title-text">
        Need help?
      </div>
    </div>
    <div class="mt-4">
      <div class="message flex flex-col gap-2">
        <div class="flex flex-col md:flex-row md:items-center gap-2">
          <div class="flex items-center gap-4 md:gap-12">
            <img src="@/assets/flexirates/Phone.png" alt="">
            <div class="message-label">
              Phone:
            </div>
          </div>
          <div class="message-content">
            {{ notify?.phone }}
          </div>
        </div>
        <div class="flex flex-col md:flex-row md:items-center gap-2">
          <div class="flex items-center gap-4 md:gap-12">
            <img src="@/assets/flexirates/Email.png" alt="">
            <div class="message-label">
              Email:
            </div>
          </div>
          <div class="message-content">
            {{ notify?.email }}
          </div>
        </div>
        <div class="flex flex-col md:flex-row md:items-center gap-2">
          <div class="flex items-center gap-4 md:gap-12">
            <img src="@/assets/flexirates/Website.png" alt="">
            <div class="message-label">
              Website:
            </div>
          </div>
          <div class="message-content underline">
            <a :href="notify?.website" target="_blank">
              {{ notify?.website }}
            </a>
          </div>
        </div>

        <div class="flex flex-col md:flex-row md:items-center gap-2">
          <div class="flex items-center gap-4 md:gap-12">
            <img src="@/assets/flexirates/Location.png" alt="">
            <div class="message-label">
              Office Address:
            </div>
          </div>
          <div class="message-content">
            {{ notify?.office_address }}
          </div>
        </div>
      </div>
    </div>
    <div class="mt-6">
      <div id="map" class="map" />
    </div>
    <div class="cancel flex flex-col md:flex-row justify-between">
      <div class="w-full md:w-2/3">
        <div class="text-xl font-bold">
          I'd like to cancel my payment arrangement.
        </div>
        <div>
          The below button will take you to a page to cancel your existing FlexiRates arrangment online. After a
          cancellation, you will need to resume
          payments by the quarterly instalments, unless you set up a new arrangement (on FlexiRates or by calling us).
        </div>
      </div>
      <div class="mt-4 md:mt-0">
        <Button label="Cancel Schedule" @click="$router.push({ name: 'supportCancellation' })" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints' as *;

.flexirates-support {
  line-height: 1.5;
}

.message {
  color: #545454;

  img {
    height: 40px;
  }

  &-label {
    width: 200px;
    font-size: 20px;
    font-weight: 700;
  }

  &-content {
    font-size: 20px;

  }
}

.map {
  width: 550px;
  height: 400px;
  background-color: #19164b;
  margin-right: 3rem;

  @include media-breakpoint-down(lg) {
    width: 100%;
    height: 400px;
    margin-right: 0;
    margin-bottom: 2rem;
  }

  @include media-breakpoint-down(md) {
    width: 100%;
    height: 350px;
  }

  @include media-breakpoint-down(sm) {
    height: 250px;
  }
}

.cancel {
  margin-top: 1.5rem;
  color: #545454;
  border-top: 1px solid #545454;
  padding: 1rem 0;
}
</style>
