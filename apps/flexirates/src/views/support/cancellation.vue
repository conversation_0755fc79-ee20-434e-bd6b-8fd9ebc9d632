<script setup lang="ts">
import { Format } from '@shared'
import { toTypedSchema } from '@vee-validate/yup'
// import dayjs from 'dayjs'
import { useToast } from 'primevue/usetoast'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import GoogleRecaptcha from '@/components/googleRecaptchaV2/index.vue'
import { home as homeApi, support as supportApi, transactions as transactionsApi } from '@/services/flexirates'

const selected = ref()
const reasonSelected = ref()
const othersReason = ref('')
const continueCancel = ref(false)
const propertyOptions = ref()
const propertyDetail = ref()

const router = useRouter()
const toast = useToast()

const recaptchaRef = ref<InstanceType<typeof GoogleRecaptcha> | null>(null)
const recaptchaVerified = ref(false)

// reCAPTCHA handlers
const onRecaptchaVerify = (response: string) => {
  if (response) {
    cancellationForm.value.google_token = response
    recaptchaVerified.value = true
  }
}
const onRecaptchaExpired = () => {
  recaptchaVerified.value = false
}

const onRecaptchaError = () => {
  recaptchaVerified.value = false
}

const isLoading = ref(false)

const cancellationDialogVisible = ref(false)

const schema = toTypedSchema(yup.object({
  verificationCode: yup.string().required('Verification Code is required'),
}))
const cancellationForm = ref({
  verificationCode: '',
  google_token: '',
})

const reasonList = ref([
  { label: 'Property Sold', value: 'Property Sold' },
  { label: 'Financial Hardship', value: 'Financial Hardship' },
  { label: 'Don\'t want to use', value: 'Don\'t want to use' },
  { label: 'Others', value: 'Others' },

])

const submitCancellation = async (values: any) => {
  const sendData = {
    remark: reasonSelected.value === 'Others' ? othersReason.value : reasonSelected.value,
    property_id: selected.value.value,
    verification_code: values.verificationCode,
  }
  verifyLoading.value = true
  try {
    const res = await supportApi.cancel(sendData)
    if (res.code === 0) {
      router.push({ name: 'support' })
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Cancellation Request successfully!',
      })
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    verifyLoading.value = false
  }
}
const next = async () => {
  if (!selected.value) {
    return toast.add({ severity: 'warn', summary: 'Tips', detail: 'Please select  a property!', life: 3000 })
  }
  isLoading.value = true
  try {
    const res = await homeApi.getPropertyDetail({ id: selected.value.value })
    if (res.code === 0) {
      propertyDetail.value = res.data
      continueCancel.value = true
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

const getColor = (status: number) => {
  if (status === 1) {
    return '#39b54a'
  }
  if (status === 5) {
    return '#eb001b'
  }
  return '#1b1548'
}

const cancelLoading = ref(false)
const verifyLoading = ref(false)

const cancelRegistration = async () => {
  if (!recaptchaVerified.value) {
    window.$toast.add({
      severity: 'warn',
      summary: 'Tips',
      detail: 'Please verify reCAPTCHA first!',
    })
  }
  if (!reasonSelected.value) {
    window.$toast.add({
      severity: 'warn',
      summary: 'Tips',
      detail: 'Please select a reason!',
    })
  }
  else if (reasonSelected.value === 'Others' && !othersReason.value?.trim()) {
    window.$toast.add({
      severity: 'warn',
      summary: 'Tips',
      detail: 'Please enter a reason!',
    })
  }
  else {
    cancelLoading.value = true
    const res = await supportApi.getVerifyCode()
    if (res.code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'The cancellation request verification code was sent successfully!',
      })
      cancelLoading.value = false
      cancellationDialogVisible.value = true
    }
  }
}

const cancelRequest = () => {
  cancellationForm.value.google_token = ''
  recaptchaVerified.value = false
  recaptchaRef.value?.reset()
  cancellationDialogVisible.value = false
}

onMounted(() => {
  transactionsApi.getAllProperty({ is_all: 1, is_include_cancel: 0 }).then((res) => {
    propertyOptions.value = res.data.map(item => ({
      label: item.street_address,
      value: item.id,
    }))
  })
})
</script>

<template>
  <div>
    <div class="flexirates-wrap">
      <div class="flexirates-title">
        <div class="flexirates-title-text">
          Cancellation Request
        </div>
      </div>
      <div class="mt-6 color-grey">
        <div class="text-xl font-semibold mb-4">
          <div v-if="!continueCancel">
            Select Property:
          </div>
          <div v-else>
            Property Details
          </div>
        </div>
        <Select
          v-model="selected" :show-clear="true" :options="propertyOptions" size="large" option-label="label"
          :disabled="continueCancel" placeholder="— Please Select —" class="w-full md:w-180"
        />
        <div v-if="continueCancel" class="grid grid-cols-1 md:grid-cols-2 mt-4">
          <div class="flex flex-col gap-4">
            <div class="flex">
              <div class="font-bold w-50">
                Property Number:
              </div>
              <div>
                {{ propertyDetail?.property_number }}
              </div>
            </div>
            <div class="flex">
              <div class="font-bold w-50">
                Schedule Status:
              </div>
              <div>
                {{ propertyDetail?.schedule_details?.payment_plan_desc }}
              </div>
            </div>
            <div class="flex">
              <div class="font-bold w-50">
                Status:
              </div>
              <div :style="{ color: getColor(propertyDetail?.status) }" class="font-bold">
                {{ propertyDetail?.status_desc }}
              </div>
            </div>
          </div>
          <!-- <div class="flex flex-col gap-4">
            <div class="flex">
              <div class="font-bold w-50">
                Property Number:
              </div>
              <div>
                36264863153468132151
              </div>
            </div>
            <div class="flex">
              <div class="font-bold w-50">
                Schedule Status:
              </div>
              <div>
                36264863153468132151
              </div>
            </div>
            <div class="flex">
              <div class="font-bold w-50">
                Property Number:
              </div>
              <div>
                36264863153468132151
              </div>
            </div>
            <div class="flex">
              <div class="font-bold w-50">
                Schedule Status:
              </div>
              <div>
                36264863153468132151
              </div>
            </div>
          </div> -->
        </div>
        <div v-if="continueCancel" class="reason mt-12">
          <div class="text-xl font-semibold mb-4">
            <div>
              Reason for Cancellation Request
            </div>
          </div>
          <Select
            v-model="reasonSelected" :show-clear="true" :options="reasonList" size="large" option-label="label"
            option-value="value" placeholder="— Please Select —" class="w-full md:w-180"
          />
          <div v-if="reasonSelected === 'Others'" class="mt-4">
            <InputText
              v-model="othersReason" name="othersReason" class="w-full md:w-180" type="text"
              placeholder="Please enter your reason"
            />
          </div>
        </div>
        <div v-if="!continueCancel" class="mt-6 flex justify-end">
          <Button label="NEXT" severity="warn" class="btn" :loading="isLoading" @click="next" />
        </div>
      </div>
    </div>
    <div v-if="continueCancel" class="overview color-grey ">
      <div class="overview-title text-xl font-semibold">
        Payment Overview
      </div>
      <!-- md:grid-cols-2 -->
      <div class="grid grid-cols-1  gap-x-26 mt-5">
        <div class="overview-left">
          <div class="mb-4">
            <span class="font-semibold mr-2">Total Rate Amount:</span>
            <span>{{ Format.formatAmount(propertyDetail?.total_amount) }}</span>
          </div>
          <div class="mb-4">
            <span class="font-semibold mr-2">Total Amount Paid:</span>
            <span>{{ Format.formatAmount(propertyDetail?.total_paid) }}</span>
          </div>
          <div class="mb-4">
            <span class="font-semibold mr-2">Total Amount Pending:</span>
            <span>{{ Format.formatAmount(propertyDetail?.total_remaining) }}</span>
          </div>
          <div class="mt-12">
            <div class="font-semibold mb-3">
              *CAPTCHA
            </div>
            <GoogleRecaptcha
              ref="recaptchaRef" class="mb-2" @verify="onRecaptchaVerify" @expired="onRecaptchaExpired"
              @error="onRecaptchaError"
            />
          </div>
        </div>
        <!-- <div v-if="propertyDetail?.transaction_history?.length" class="overview-right pr-16">
          <div class="flex justify-between mb-4">
            <div class="font-semibold">
              Payment Amount
            </div>
            <div class="font-semibold">
              Due Date
            </div>
          </div>
          <div v-for="(item, index) in propertyDetail?.transaction_history" :key="index" class="flex justify-between mb-2">
            <div>
              {{ Format.formatAmount(item.payment_amount) }}
            </div>
            <div>
              {{ dayjs(item.payment_date).format('DD/MM/YY') }}
            </div>
          </div>
        </div> -->
      </div>
      <div class="flex justify-between mt-12">
        <Button label="BACK" class="btn" @click="$router.back()" />
        <Button
          label="CANCEL REGISTRATION" severity="warn" class="btn" :loading="cancelLoading"
          @click="cancelRegistration"
        />
      </div>
    </div>
    <div class="dialog">
      <CustomDialog
        title="Cancellation Request Verification" :visible="cancellationDialogVisible"
        @update:visible="(val) => (cancellationDialogVisible = val)"
      >
        <template #content>
          <VeeForm :validation-schema="schema" :initial-values="cancellationForm" @submit="submitCancellation">
            <div class="flex flex-col gap-8 mt-8 text-lg">
              <div>You've selected <span class="font-semibold text-(--color-btn-warn)">{{ reasonSelected }}</span></div>
              <div v-if="reasonSelected === 'Others'">
                Additional Note: <span>{{ othersReason }}</span>
              </div>
              <div>
                To verify this request, we've sent a verification code to your email.
              </div>
              <div>
                Please enter the code below to confirm your are the authorised user.
              </div>
            </div>
            <Field
              v-slot="{ field, errorMessage, handleChange }" v-model="cancellationForm.verificationCode" as="div"
              name="verificationCode"
            >
              <div class="flex items-center mt-8">
                <div class="font-bold w-80">
                  Verification Code
                </div>
                <InputText
                  v-model="field.value" name="verificationCode" type="text" class="w-full"
                  @value-change="handleChange"
                />
              </div>
              <Message v-if="errorMessage" severity="error" variant="simple" class="ml-51 mt-2">
                {{
                  errorMessage }}
              </Message>
            </Field>
            <div class="flex justify-end gap-4 mt-6">
              <Button label="CANCEL" class="verify-btn btn" @click="cancelRequest" />
              <Button label="VERIFY AND SUBMIT" severity="warn" class="verify-btn btn" type="submit" :loading="verifyLoading" />
            </div>
          </VeeForm>
        </template>
      </CustomDialog>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/styles/mixins/breakpoints.scss' as *;

.color-grey {
  color: #545454;
}

.btn {
  padding: 6px 2rem;
}

.verify-btn {
  width: 220px;
}

.overview {
  padding: 24px;
  background-color: var(--color-white);
  border-radius: 16px;
  margin-top: 2rem;

  .overview-left {
    border-right: 1px solid #545454;

    @include media-breakpoint-down(sm) {
      border-right: none;
      border-bottom: 1px solid #545454;
    }
  }
}
</style>
