<script setup lang="ts">
import type { FormContext } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/yup'
import { Field, Form as VeeForm } from 'vee-validate'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as yup from 'yup'
import { user as userApi } from '@/services/flexirates'
import { useUserStore } from '@/store/modules/user'

const router = useRouter()

enum EditType {
  Email = 'email',
  Phone = 'phone',
  PersonalDetails = 'personalDetails',
}

const userStore = useUserStore()

const isPersonalDetailsExpanded = ref(true)

const isLocationExpanded = ref(true)

const isSubmitLoading = ref(false)

const profileForm = ref<FormContext>()

const locationForm = ref<FormContext>()

const emailForm = ref<FormContext>()

const phoneForm = ref<FormContext>()

const showType = ref(EditType.PersonalDetails)

const userModel = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
})

const locationModel = ref({
  line_1: '',
  line_2: '',
  city: '',
  state: '',
  postcode: '',
  country: '',
})

const emailModel = ref({
  email: '',
})

const phoneModel = ref({
  phone: '',
})

const profileSchema = toTypedSchema(yup.object({
  name: yup.string().required('Name is required'),
}))

const locationSchema = toTypedSchema(yup.object({
  line_1: yup.string().optional(),
  line_2: yup.string().optional(),
  city: yup.string().optional(),
  state: yup.string().optional(),
  zip: yup.string().optional(),
  country: yup.string().optional(),
}))

const emailSchema = toTypedSchema(yup.object({
  email: yup.string().email('Invalid email address').required('Email is required'),
}))

const phoneSchema = toTypedSchema(yup.object({
  phone: yup.string().required('Phone is required'),
}))

const isLoading = ref(false)

const fetchUserInfo = async () => {
  isLoading.value = true
  try {
    const userData = await userStore.getUserInfo()
    if (userData) {
      userModel.value.first_name = userData.first_name
      userModel.value.last_name = userData.last_name
      userModel.value.email = userData.email
      userModel.value.phone = userData.mobile

      locationModel.value.line_1 = userData.location.address_line_one
      locationModel.value.line_2 = userData.location.address_line_two
      locationModel.value.city = userData.location.city
      locationModel.value.state = userData.location.state
      locationModel.value.postcode = userData.location.postcode
      locationModel.value.country = userData.location.country
    }
  }
  finally {
    isLoading.value = false
  }
}

const updateProfile = async (type: string) => {
  isSubmitLoading.value = true
  try {
    let sendData: any = {}
    switch (type) {
      case 'email':
        sendData = {
          email: emailModel.value.email,
        }
        break
      case 'mobile':
        sendData = {
          mobile: phoneModel.value.phone,
        }

        break
      case 'location':
        sendData = {
          first_name: userModel.value.first_name,
          last_name: userModel.value.last_name,
          location: {
            ...locationModel.value,
            address_line_1: locationModel.value.line_1,
            address_line_2: locationModel.value.line_2,
          },
        }
        delete sendData?.location.line_1
        delete sendData?.location.line_2
        break
      default:
        break
    }
    const { code } = await userApi.updatePersonalInfo(sendData)
    console.log('code', code)

    if (code === 0) {
      window.$toast.add({
        severity: 'success',
        summary: 'Update Profile',
        detail: 'Profile updated successfully',
      })
      router.push({ name: 'profile' })
      fetchUserInfo()
    }
  }
  finally {
    isSubmitLoading.value = false
  }
}

// 加载用户数据
onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div class="profile-page flex flex-col gap-8">
    <!-- Loading overlay for initial data fetch -->
    <div v-if="isLoading" class="loading-overlay w-full flex items-center justify-center h-45">
      <div class="loading-content">
        <ProgressSpinner style="width: 50px; height: 50px" stroke-width="4" />
        <p class="loading-text">
          Loading profile data...
        </p>
      </div>
    </div>

    <div v-if="showType === EditType.PersonalDetails && !isLoading" class="flex flex-col gap-8">
      <div class="card px-8 py-6 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="text-3xl font-bold profile-subtitle">
            Personal Details
          </div>
          <!-- 展开/收起 -->
          <Button
            :icon="isPersonalDetailsExpanded ? 'pi pi-angle-up' : 'pi pi-angle-down'" text
            @click="isPersonalDetailsExpanded = !isPersonalDetailsExpanded"
          />
        </div>

        <div class="collapsible-content" :class="{ expanded: isPersonalDetailsExpanded }">
          <div class="content-wrapper">
            <VeeForm
              ref="profileForm" class="flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8"
              :validation-schema="profileSchema" :initial-values="userModel"
            >
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="userModel.first_name" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="first_name" label="First Name"
              >
                <label for="first_name" class="w-60">First Name</label>
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="max-w-full lg:w-full" :class="{ 'p-invalid': errorMessage }"
                    :disabled="isSubmitLoading"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="userModel.last_name" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="last_name" label="Last Name"
              >
                <label for="last_name" class="w-60">Last Name</label>
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    :disabled="isSubmitLoading"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field as="div" class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10" name="email" label="Email">
                <label for="email" class="lg:w-60">Email</label>
                <div class="flex justify-between items-center gap-2">
                  <div class="max-w-45 lg:max-w-60 xl:min-w-140 line-clamp-1 text-ellipsis" :title="userModel.email">
                    {{ userModel.email }}
                  </div>
                  <Button
                    label="Update Email Address" class="!p-0 personal-update-button" text
                    :disabled="isSubmitLoading"
                    @click="showType = EditType.Email"
                  />
                </div>
              </Field>
              <Field as="div" class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10" name="phone" label="Phone">
                <label for="phone" class="lg:w-60">Phone</label>
                <div class="flex justify-between items-center gap-2">
                  <div class="lg:min-w-60 xl:min-w-140">
                    {{ userModel.phone }}
                  </div>
                  <Button
                    label="Update Mobile Number" class="!p-0 personal-update-button" text
                    :disabled="isSubmitLoading"
                    @click="showType = EditType.Phone"
                  />
                </div>
              </Field>
            </VeeForm>
          </div>
        </div>
      </div>
      <div class="card p-4 rounded-md">
        <div class="flex items-center justify-between">
          <div class="text-3xl font-bold profile-subtitle">
            Location
          </div>
          <!-- 展开/收起 -->
          <Button
            :icon="isLocationExpanded ? 'pi pi-angle-up' : 'pi pi-angle-down'" text
            @click="isLocationExpanded = !isLocationExpanded"
          />
        </div>

        <div class="collapsible-content" :class="{ expanded: isLocationExpanded }">
          <div class="content-wrapper">
            <VeeForm
              ref="locationForm" class="flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8"
              :validation-schema="locationSchema" :initial-values="locationModel"
            >
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="locationModel.line_1" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="line_1"
              >
                <label for="line_1" class="w-60">Mailing Address (optional)</label>
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="Address Line 1" :disabled="isSubmitLoading" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="locationModel.line_2" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="line_2"
              >
                <label for="line_2" class="w-60" />
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="Address Line 2" :disabled="isSubmitLoading" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="locationModel.city" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="city"
              >
                <label for="city" class="w-60" />
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="City" :disabled="isSubmitLoading" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="locationModel.state" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="state"
              >
                <label for="state" class="w-60" />
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="State" :disabled="isSubmitLoading" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="locationModel.postcode" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="zip"
              >
                <label for="postcode" class="w-60" />
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="Postcode" :disabled="isSubmitLoading" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="locationModel.country" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2" name="country"
              >
                <label for="country" class="w-60" />
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="Country" :disabled="isSubmitLoading" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
            </VeeForm>
          </div>
        </div>
      </div>
      <div class="flex flex-col md:flex-row justify-center gap-4">
        <Button label="CANCEL" class="md:w-80 h-12 cancel-button" />
        <Button label="UPDATE" class="md:w-80 h-12" severity="warn" :loading="isSubmitLoading" @click="updateProfile('location')" />
      </div>
    </div>
    <div v-if="showType === EditType.Email" class="flex flex-col gap-8">
      <div class="card px-8 py-6 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="text-3xl font-bold text-gray-700">
            Personal Details
          </div>
          <!-- 展开/收起 -->
          <Button
            :icon="isPersonalDetailsExpanded ? 'pi pi-angle-up' : 'pi pi-angle-down'" text
            @click="isPersonalDetailsExpanded = !isPersonalDetailsExpanded"
          />
        </div>
        <div class="collapsible-content" :class="{ expanded: isPersonalDetailsExpanded }">
          <div class="content-wrapper">
            <VeeForm
              ref="emailForm" class="flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8"
              :validation-schema="emailSchema" :initial-values="emailModel"
            >
              <div class="text-xl font-bold text-gray-700">
                Update email
              </div>

              <div class="border-b-1 border-gray-300" />

              <p class="text-gray-500 mb-1">
                Enter your new email address and we'll email you out a confirmation message to that address.
              </p>

              <strong class="mb-1">Please follow the link in the email we send to confirm your new address.</strong>

              <p class="text-gray-500 mb-8 mb-1">
                Your new email address won't be reflected until you've confirmed it.
              </p>

              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="emailModel.email" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10" name="email"
              >
                <label for="email" class="w-60">New Email Address</label>
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    placeholder="Enter your new email address" @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>

              <div class="flex flex-col md:flex-row justify-center gap-4 mt-8">
                <Button
                  label="CANCEL" class="md:w-80 h-12 cancel-button"
                  @click="showType = EditType.PersonalDetails"
                />
                <Button label="UPDATE" class="md:w-80 h-12" severity="warn" :loading="isSubmitLoading" @click="updateProfile('email')" />
              </div>
            </VeeForm>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showType === EditType.Phone" class="flex flex-col gap-8">
      <div class="card px-8 py-6 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="text-3xl font-bold text-gray-700">
            Personal Details
          </div>
          <!-- 展开/收起 -->
          <Button
            :icon="isPersonalDetailsExpanded ? 'pi pi-angle-up' : 'pi pi-angle-down'" text
            @click="isPersonalDetailsExpanded = !isPersonalDetailsExpanded"
          />
        </div>

        <div class="collapsible-content" :class="{ expanded: isPersonalDetailsExpanded }">
          <div class="content-wrapper">
            <VeeForm
              ref="phoneForm" class="flex flex-col space-y-4 border-t-[3px] border-gray-600 pt-8"
              :validation-schema="phoneSchema" :initial-values="phoneModel"
            >
              <div class="text-xl font-bold text-gray-700">
                Update mobile number
              </div>

              <div class="border-b-1 border-gray-300" />

              <p class="text-gray-500 mb-1">
                Enter your new mobile number and we'll send a confirmation code to that new number.
              </p>
              <strong class="mb-8">Please enter the code we send to confirm your new mobile number.</strong>

              <Field
                v-slot="{ field, handleChange, errorMessage }" v-model="phoneModel.phone" as="div"
                class="flex flex-col lg:flex-row lg:items-center gap-2 min-h-10" name="phone"
              >
                <label for="phone" class="w-60">Mobile Number</label>
                <div class="flex flex-col gap-2 lg:min-w-140">
                  <InputText
                    v-model="field.value" class="w-full" :class="{ 'p-invalid': errorMessage }"
                    @value-change="handleChange"
                  />
                  <Message v-if="errorMessage" severity="error" variant="simple" class="mt-2">
                    {{ errorMessage }}
                  </Message>
                </div>
              </Field>
              <div class="flex flex-col md:flex-row justify-center gap-4 mt-8">
                <Button
                  label="CANCEL" class="md:w-80 h-12 cancel-button"
                  @click="showType = EditType.PersonalDetails"
                />
                <Button label="UPDATE" class="md:w-80 h-12" severity="warn" :loading="isSubmitLoading" @click="updateProfile('mobile')" />
              </div>
            </VeeForm>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.profile-page {
  min-height: 80vh;
  color: var(--color-gray-500);

  .profile-subtitle {
    color: #031f73;
  }

  .card {
    border-radius: var(--border-radius);
    padding: 24px;
    background-color: var(--color-white);
  }

  .personal-update-button {
    text-decoration: underline;
    color: var(--color-gray-500);
  }

  .collapsible-content {
    display: grid;
    grid-template-rows: 0fr;
    overflow: hidden;
    transition: grid-template-rows 0.3s ease-out;

    &.expanded {
      grid-template-rows: 1fr;
    }

    .content-wrapper {
      min-height: 0;
      padding-top: 1rem;
    }
  }

  .cancel-button {
    background-color: transparent !important;
    color: var(--color-gray-500);
    --p-button-primary-border-color: #1E1E1E;

    &:hover {
      color: var(--color-gray-500);
    }
  }
}
</style>
