{"name": "@bill-merchant/flexirates", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 3002 --open", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && vite build --mode test", "build:uat-production": "vue-tsc -b && vite build --mode uat-production", "preview": "vite preview --port 4000"}, "dependencies": {"@bill-merchant/shared": "workspace:*", "@bill-merchant/ui-components": "workspace:*", "@googlemaps/js-api-loader": "^1.16.8", "echarts": "^5.6.0", "swiper": "^11.2.6", "qrcode": "^1.5.4"}}