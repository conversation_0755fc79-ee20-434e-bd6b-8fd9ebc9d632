import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { HttpResponse } from '@/types/http'
import axios from 'axios'
import i18n from '@/i18n'
import router from '@/router'
import { useUserStore } from '@/store/modules/user'
// Error types
export class ApiError extends Error {
  constructor(
    public status: number,
    public message: string,
    public data?: any,
  ) {
    super(message)
    this.name = 'ApiError'
    this.init()
  }

  init() {
    window.$confirm.require({
      message: this.message,
      header: this.name,
      icon: 'pi pi-exclamation-triangle',
      acceptProps: {
        label: 'dismiss',
        class: '!ml-40',
      },
      rejectProps: {
        style: {
          display: 'none',
        },
      },
    })
  }
}

// Create axios instance
const http: AxiosInstance = axios.create({
  baseURL: `${import.meta.env.VITE_API_BASE_URL}/merchant`,
  timeout: import.meta.env.VITE_API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
http.interceptors.request.use(
  (config) => {
    const { token, expiresAt } = useUserStore()
    if (token && expiresAt && Date.now() > expiresAt) {
      return Promise.reject(new Error('Token expired'))
    }
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    config.headers['x-timezone'] = Intl.DateTimeFormat().resolvedOptions().timeZone
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// Response interceptor
http.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, status } = response
    switch (data.code) {
      case 1:
        window.$toast?.add({
          severity: 'error',
          summary: 'Error',
          detail: data.message || i18n.global.t('common.forbidden'),
        })
        return response.data
      case 403:
        router.push({ path: `${window.location.origin}/two-factor-auth` })
        return response.data
      default:
        response.data = {
          code: data?.code,
          data: data?.data,
          message: data?.message || i18n.global.t('common.success'),
          status,
        }
        return response.data
    }
  },
  (error) => {
    const { setToken } = useUserStore()
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 401:
          setToken('')
          router.replace({ name: 'login' })
          break
        case 403:
        case 422:
          window.$toast?.add({
            severity: 'error',
            summary: 'Error',
            detail: data.message || i18n.global.t('common.forbidden'),
            life: 3000,
          })
          break
        case 500:
          window.$toast?.add({
            severity: 'error',
            summary: 'Error',
            detail: i18n.global.t('common.internalServerError'),
            life: 3000,
          })
          break
      }
      return Promise.reject(
        new ApiError(
          status,
          data.message || i18n.global.t('common.anErrorOccurred'),
          data,
        ),
      )
    }

    // 怎么判断 error 是 token 过期?
    if (error?.message === 'Token expired') {
      setToken('')
      router.replace({ name: 'login' })
      return new ApiError(
        500,
        'Token expired, please login again',
        error,
      )
    }

    return Promise.reject(
      new ApiError(
        500,
        i18n.global.t('common.networkError'),
        error,
      ),
    )
  },
)

// Request methods
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<HttpResponse<T>> => {
    return http.get(url, config)
  },
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<HttpResponse<T>> => {
    return http.post(url, data, config)
  },
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<HttpResponse<T>>> => {
    return http.put(url, data, config)
  },
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<HttpResponse<T>> => {
    return http.delete(url, config)
  },
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<HttpResponse<T>> => {
    return http.patch(url, data, config)
  },
}

export default http
