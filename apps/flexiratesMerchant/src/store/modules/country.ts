import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { country as countryApi } from '@/services/api'

export const useCountryStore = defineStore('country', () => {
  // State
  const countriesData = ref<Country.Info[]>([])
  const isLoading = ref(false)
  const isLoaded = ref(false)
  const error = ref<string | null>(null)

  // Actions
  const fetchCountries = async (): Promise<void> => {
    if (isLoaded.value || isLoading.value) {
      return
    }

    isLoading.value = true
    error.value = null

    try {
      const { code, data } = await countryApi.getList()

      if (code === 0) {
        countriesData.value = data
        isLoaded.value = true
      }
      else {
        error.value = 'Failed to fetch countries'
      }
    }
    catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
    }
    finally {
      isLoading.value = false
    }
  }

  // 重置状态，强制重新加载
  const resetCountries = (): void => {
    countriesData.value = []
    isLoaded.value = false
    error.value = null
  }

  // Getters - 懒加载：第一次访问时自动触发数据获取
  const countries = computed((): Country.Info[] => {
    if (!isLoaded.value && !isLoading.value) {
      fetchCountries()
    }
    return countriesData.value
  })

  return {
    // Getters
    countries,
    isLoading,
    isLoaded,
    error,
    countriesData,

    // Actions
    fetchCountries,
    resetCountries,
  }
}, {
  persist: false,
})
