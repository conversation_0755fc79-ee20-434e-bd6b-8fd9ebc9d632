<script setup lang="ts">
import { Format } from '@shared'
import { CommonPage } from '@ui'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useDict } from '@/composables/useDict'
import { settlementDetailInfo as settlementDetailInfoApi } from '@/services/api'

defineOptions({
  name: 'settlementDetailInfoDetail',
})

const route = useRoute()
const toast = useToast()

// 响应式数据
const settlementDetailData = ref<SettlementDetailInfo.Info | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// 获取结算明细类型字典
const { getLabel: getAmountTypeLabel } = useDict('settlement_detail_amount_type')

// 获取结算详情数据
const fetchData = async () => {
  try {
    loading.value = true
    error.value = null

    const res = await settlementDetailInfoApi.getDetail({
      id: Number(route.params.id),
    })

    settlementDetailData.value = res.data
  }
  catch (err: any) {
    error.value = err.message || 'Failed to load settlement detail information'
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load settlement detail information',
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <CommonPage :loading="loading" :error="error" @retry="fetchData">
    <div v-if="settlementDetailData" class="settlement-detail-info-content">
      <div class="bg-white p-6 rounded-2xl my-3 text-gray-600">
        <div class="text-2xl font-bold">
          Settlement Detail - {{ settlementDetailData.trans_no }}
        </div>
      </div>

      <div class="bg-white px-6 py-2 rounded-2xl mt-8 text-gray-600">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">
            Settlement Detail Information
          </h1>
        </div>

        <!-- 基本交易信息 -->
        <div class="info-section">
          <h3 class="section-title">
            Transaction Information
          </h3>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Transaction Number</label>
                <div class="detail-value">
                  {{ settlementDetailData.trans_no || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Customer ID</label>
                <div class="detail-value">
                  {{ settlementDetailData.customer_id || '-' }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Card Type</label>
                <div class="detail-value">
                  {{ settlementDetailData.card_type }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Credit Brand</label>
                <div class="detail-value">
                  {{ settlementDetailData.credit_brand }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 金额信息 -->
        <div class="info-section">
          <h3 class="section-title">
            Amount Information
          </h3>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Currency</label>
                <div class="detail-value">
                  {{ settlementDetailData.currency || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Amount</label>
                <div class="detail-value amount-highlight">
                  {{ Format.formatAmount(settlementDetailData.amount) }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Transaction Currency</label>
                <div class="detail-value">
                  {{ settlementDetailData.trans_currency || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Transaction Amount</label>
                <div class="detail-value">
                  {{ Format.formatAmount(settlementDetailData.trans_amount) }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Currency</label>
                <div class="detail-value">
                  {{ settlementDetailData.settlement_currency || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Amount</label>
                <div class="detail-value amount-highlight">
                  {{ Format.formatAmount(settlementDetailData.settlement_amount) }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Exchange Rate</label>
                <div class="detail-value">
                  {{ settlementDetailData.rate || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Amount Type</label>
                <div class="detail-value">
                  {{ getAmountTypeLabel(settlementDetailData.amount_type) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="info-section">
          <h3 class="section-title">
            Additional Information
          </h3>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Date</label>
                <div class="detail-value">
                  {{ settlementDetailData.settle_at || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Remarks</label>
                <div class="detail-value">
                  {{ settlementDetailData.remarks || '-' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </CommonPage>
</template>

<style scoped lang="scss">
.settlement-detail-info-detail {
  margin: 0 auto;
}

// Loading 状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .loading-text {
    margin-top: 1rem;
    color: #6b7280;
    font-size: 1rem;
  }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

// 页面头部
.page-header {
  margin-top: 0;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--colors-gray);
  padding-bottom: 1cap;
  display: flex;
  justify-content: space-between;

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #031F73;
    margin-bottom: 0.5rem;
  }
}

// 信息区域
.info-section {
  margin-bottom: 2rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #031F73;
    margin-bottom: 1rem;
  }
}

// 表单布局
.form-row {
  display: flex;
  gap: 5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

.field {
  display: flex;
  justify-content: flex-start;
  gap: 1rem;

  .field-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--colors-gray);

    display: flex;
    align-items: center;
    width: 200px;
  }
}

// 详情值样式
.detail-value {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #545454;
  border-radius: 1rem;
  color: #374151;
  font-size: 0.95rem;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;

  &:empty::before {
    content: '-';
    color: #9ca3af;
  }

  // 金额高亮
  &.amount-highlight {
    background-color: #ecfdf5;
    border-color: #10b981;
    color: #065f46;
    font-weight: 600;
  }

  // 欠款金额样式
  &.amount-owing {
    background-color: #fef2f2;
    border-color: #f87171;
    color: #991b1b;
    font-weight: 600;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .settlement-detail-info-detail {
    padding: 1rem;
  }

  .page-header {
    .page-title {
      font-size: 1.75rem;
    }
  }

  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}

// 小型移动设备
@media screen and (max-width: 480px) {
  .settlement-detail-info-detail {
    padding: 0.75rem;
  }

  .info-section {
    margin-bottom: 1rem;

    .section-title {
      font-size: 1.125rem;
    }
  }

  .detail-value {
    font-size: 0.9rem;
    padding: 0.6rem 0.8rem;
  }

  .field-label {
    font-size: 0.8rem;
  }
}
</style>
