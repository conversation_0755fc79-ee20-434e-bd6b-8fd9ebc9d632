<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { Format } from '@shared'
import { computed, onActivated, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BaseDataTable from '@/components/common/BaseDataTable.vue'
import BaseSearch from '@/components/common/BaseSearch.vue'
import { useDict } from '@/composables/useDict'
import { useListRefresh } from '@/composables/useListRefresh'
import { usePermissions } from '@/composables/usePermissions'
import { useRequestList } from '@/composables/useRequestList'
import { Permissions } from '@/constants/permissions'
import { SearchFieldType } from '@/constants/search'
import { settlement as settlementApi } from '@/services/api'
import { formatDate } from '@/utils/date'

defineOptions({
  name: 'settlementDetailList',
})

const route = useRoute()
const router = useRouter()

// 列配置
const columns = ref<TableColumnItem[]>([
  { field: 'business_id', header: 'BID', style: { minWidth: '160px' } },
  { field: 'settlement_currency', header: 'Settlement Currency', style: { minWidth: '140px' } },
  { field: 'settlement_amount', template: 'settlement_amount', header: 'Current Settlement Amount', style: { minWidth: '180px' } },
  { field: 'settle_fee', template: 'settle_fee', header: 'Current Fee', style: { minWidth: '120px' } },
  { field: 'settlement_type', template: 'settlement_type', header: 'Settlement Method', style: { minWidth: '140px' } },
  { field: 'settle_at', template: 'settle_at', header: 'Settlement Date', style: { minWidth: '140px' } },
])

const { hasPermission } = usePermissions()

// 使用 useRequestList 处理列表数据
const {
  list,
  loading,
  total,
  refresh,
  search,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
  loading: isListLoading,
  setSearchParams,
} = useRequestList<Settlement.Info[], Api.SettlementListReq>({
  requestFn: settlementApi.getList,
})

// 使用通用的列表刷新逻辑
useListRefresh('flexiratesMerchantSettlementList', refresh)

const handleSort = (event: Record<string, any>) => {
  const { sortField, sortOrder } = event
  setSearchParams({
    sort_by: sortField,
    sort_order: sortOrder === 1 ? 'asc' : 'desc',
  })
  search()
}

const searchModel = ref<Partial<Api.SettlementListReq>>({
  settlement_type: undefined,
  settle_at: '',
})

const tableSelection = ref([])

const settlementTable = ref()

// 获取结算类型字典
const { getLabel: getSettlementTypeLabel, options: settlementTypeOptions } = useDict('settlement_type')

// 配置搜索字段
const searchFields = computed(() => [
  {
    name: 'settlement_type',
    label: 'Settlement Type',
    type: SearchFieldType.SELECT,
    placeholder: 'Select Settlement Type',
    options: [{ label: 'All', value: undefined }, ...settlementTypeOptions.value],
    defaultValue: undefined,
  },
])

const moreSearchFields = computed(() => [
  {
    name: 'settle_at[]',
    label: 'Settlement Date',
    type: SearchFieldType.DATE_RANGE,
    placeholder: 'Select Remit Date Range',
    defaultValue: '',
  },
])

const navigateToDetail = ({ data }: { data: any }) => {
  if (!hasPermission(Permissions.SETTLEMENT_DETAIL)) {
    return
  }
  router.push({ name: 'settlementDetail', params: { id: data.id } })
}

// 搜索处理
const handleSearch = () => {
  setSearchParams(searchModel.value)
  search()
}

onActivated(() => {
  const query = route.query
  if (query && Object.keys(query).length > 0) {
    // First update the searchModel with properly converted values
    searchModel.value = {
      settlement_type: query.settlement_type ? Number(query.settlement_type) : undefined,
      settle_at: typeof query.settle_at === 'string' ? query.settle_at : '',
    }

    // Then set search params and execute search
    setSearchParams(searchModel.value)
    refresh()
  }
  else {
    searchModel.value = {
      settlement_type: undefined,
      settle_at: '',
    }
    setSearchParams(searchModel.value)
    refresh()
  }
})
</script>

<template>
  <div class="settlement-page">
    <BaseSearch
      v-model="searchModel" :loading="isListLoading" :basic-search-fields="searchFields"
      :advanced-search-fields="moreSearchFields" @search="handleSearch"
    />

    <!-- Settlement表格 -->
    <BaseDataTable
      ref="settlementTable" v-model:selection="tableSelection"
      :show-search-bar="false" :value="list" :columns="columns" :scrollable="true" :show-multiple-column="false"
      :loading="loading" :paginator="true" :rows="50" :total-records="total" :lazy="true" data-key="id" :failed="failed" :row-hover="true"
      :failure-message="failureMessage" :striped-rows="false" style="--frozen-column-border-bottom : -8px"
      @change-search="handleSearch" @page="(e: DataTablePageEvent) => handlePageChange(e)" @row-click="navigateToDetail"
      @sort="handleSort"
    >
      <template #settlement_amount="{ data }">
        <span v-if="data?.settlement_amount">
          {{ Format.formatAmount(data?.settlement_amount) }}
        </span>
      </template>
      <template #settle_fee="{ data }">
        <span v-if="data?.settle_fee">
          {{ Format.formatAmount(data?.settle_fee) }}
        </span>
      </template>
      <template #settlement_type="{ data }">
        {{ getSettlementTypeLabel(data?.settlement_type) }}
      </template>
      <template #settle_at="{ data }">
        {{ formatDate(data.settle_at) }}
      </template>
    </BaseDataTable>
  </div>
</template>

<style lang="scss" scoped>
.settlement-page {
  .p-dialog .p-dialog-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1.5rem;
  }

  .p-dialog .p-dialog-footer {
    border-top: 1px solid #dee2e6;
    padding: 1.5rem;
    text-align: right;
  }

  .p-dialog .p-dialog-content {
    padding: 2rem;
  }
}
</style>
