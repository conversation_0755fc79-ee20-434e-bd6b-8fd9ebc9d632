<script setup lang="ts">
import { Format } from '@shared'
import { CommonPage } from '@ui'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useDict } from '@/composables/useDict'
import { settlement as settlementApi } from '@/services/api'

defineOptions({
  name: 'SettlementDetail',
})

const route = useRoute()
const toast = useToast()

// 响应式数据
const settlementData = ref<Settlement.Info | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// 获取结算类型字典
const { getLabel: getSettlementTypeLabel } = useDict('settlement_type')

// 获取结算详情数据
const fetchData = async () => {
  try {
    loading.value = true
    error.value = null

    const res = await settlementApi.getDetail({
      id: Number(route.params.id),
    })

    settlementData.value = res.data
  }
  catch (err: any) {
    error.value = err.message || 'Failed to load settlement details'
    toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Failed to load settlement details',
      life: 3000,
    })
  }
  finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <CommonPage :loading="loading" :error="error" @retry="fetchData">
    <div v-if="settlementData" class="settlement-content">
      <div class="bg-white p-6 rounded-2xl my-3 text-gray-600">
        <div class="text-2xl font-bold">
          Settlement Details - {{ settlementData.business_id }}
        </div>
      </div>

      <div class="bg-white px-6 py-2 rounded-2xl mt-8 text-gray-600">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">
            Settlement Information
          </h1>
        </div>

        <!-- 基本结算信息 -->
        <div class="info-section">
          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">BID</label>
                <div class="detail-value">
                  {{ settlementData.business_id || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Currency</label>
                <div class="detail-value">
                  {{ settlementData.settlement_currency || '-' }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Current Settlement Amount</label>
                <div class="detail-value amount-highlight">
                  {{ Format.formatAmount(settlementData.settlement_amount) }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Current Fee</label>
                <div class="detail-value">
                  {{ Format.formatAmount(settlementData.settle_fee) }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Chargeback Amount</label>
                <div class="detail-value">
                  {{ settlementData.chargeback_amount ? Format.formatAmount(settlementData.chargeback_amount) : '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Chargeback Fee</label>
                <div class="detail-value">
                  {{ settlementData.chargeback_fee ? Format.formatAmount(settlementData.chargeback_fee) : '-' }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Fee</label>
                <div class="detail-value">
                  {{ settlementData.settlement_fee ? Format.formatAmount(settlementData.settlement_fee) : '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Method</label>
                <div class="detail-value">
                  {{ getSettlementTypeLabel(settlementData.settlement_type) }}
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-col">
              <div class="field">
                <label class="field-label">Settlement Date</label>
                <div class="detail-value">
                  {{ settlementData.settle_at || '-' }}
                </div>
              </div>
            </div>

            <div class="form-col">
              <!-- Empty column for layout balance -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </CommonPage>
</template>

<style scoped lang="scss">
.settlement-detail {
  margin: 0 auto;
}

// Loading 状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .loading-text {
    margin-top: 1rem;
    color: #6b7280;
    font-size: 1rem;
  }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

// 页面头部
.page-header {
  margin-top: 0;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--colors-gray);
  padding-bottom: 1cap;
  display: flex;
  justify-content: space-between;

  .page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #031F73;
    margin-bottom: 0.5rem;
  }
}

// 信息区域
.info-section {
  margin-bottom: 2rem;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #031F73;
    margin-bottom: 1rem;
  }
}

// 表单布局
.form-row {
  display: flex;
  gap: 5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

.field {
  display: flex;
  justify-content: flex-start;
  gap: 1rem;

  .field-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--colors-gray);

    display: flex;
    align-items: center;
    width: 200px;
  }
}

// 详情值样式
.detail-value {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #545454;
  border-radius: 1rem;
  color: #374151;
  font-size: 0.95rem;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;

  &:empty::before {
    content: '-';
    color: #9ca3af;
  }

  // 金额高亮
  &.amount-highlight {
    background-color: #ecfdf5;
    border-color: #10b981;
    color: #065f46;
    font-weight: 600;
  }

  // 欠款金额样式
  &.amount-owing {
    background-color: #fef2f2;
    border-color: #f87171;
    color: #991b1b;
    font-weight: 600;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .settlement-detail {
    padding: 1rem;
  }

  .page-header {
    .page-title {
      font-size: 1.75rem;
    }
  }

  .form-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-col {
    min-width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}

// 小型移动设备
@media screen and (max-width: 480px) {
  .settlement-detail {
    padding: 0.75rem;
  }

  .info-section {
    margin-bottom: 1rem;

    .section-title {
      font-size: 1.125rem;
    }
  }

  .detail-value {
    font-size: 0.9rem;
    padding: 0.6rem 0.8rem;
  }

  .field-label {
    font-size: 0.8rem;
  }
}
</style>
