<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import dayjs from 'dayjs'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useRequestList } from '@/composables/useRequestList'
import { ratepayer as ratepayerApi } from '@/services/api/index'

const route = useRoute()

const requestList = useRequestList<Api.CustomerActivityLogListData, Api.CustomerActivityLogListReq>({
  page_size: 10,
  defaultParams: { customer_id: route.query?.customerId as string },
  requestFn: ratepayerApi.getActivityLog,
})

const columns = ref<TableColumnItem[]>([
  {
    field: '',
    header: 'Date & Time',
    style: { width: '200px', padding: '0 8px' },
    template: 'date',
  },
  {
    field: 'activity_type_desc',
    header: 'Activity Type',
    style: { width: '300px' },
  },
  {
    field: 'activity_detail',
    header: 'Details',
  },
  {
    field: 'status',
    header: 'Status',
    style: { width: '100px' },
    template: 'status',
  },
])

const {
  list,
  loading,
  total,
  onPageChange: handlePageChange,
  failed,
  failureMessage,
} = requestList

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 重置数据
const resetList = () => {
  currentPage.value = 1
  requestList.refresh()
}

// 加载更多
const loadingMore = ref(false)
const moreList = ref<any>([])
const loadMore = async () => {
  if (loadingMore.value) { return }
  loadingMore.value = true
  try {
    const nextPage = currentPage.value + 1
    const res = await ratepayerApi.getActivityLog({
      page: nextPage,
      page_size: pageSize.value,
      customer_id: route.query?.customerId as string,
    })
    if (Array.isArray(res?.data?.data) && res.data.data.length > 0) {
      moreList.value = res.data.data
      currentPage.value = nextPage
    }
  }
  finally {
    loadingMore.value = false
  }
}

// 初始化加载
resetList()
const combinedList = computed(() => {
  return [...list.value, ...moreList.value]
})
</script>

<template>
  <div>
    <BaseDataTable
      :value="combinedList" :columns="columns" :loading="loading" :show-multiple-column="false"
      :paginator="false" :show-search-bar="false" :rows="10" :lazy="true" data-key="id" :failed="failed"
      :failure-message="failureMessage" :row-hover="false" :total-records="total" class="activity-log-table"
      @refresh="resetList" @page="(e: DataTablePageEvent) => handlePageChange(e)"
    >
      <template #date="{ data }">
        {{ dayjs(data?.activity_at).format('DD MMM YYYY, HH:mm A') }}
      </template>
      <template #status="{ data }">
        <span
          class="font-semibold" :class="{
            'text-green-500': data?.activity_status === 1,
            'text-red-500': data?.activity_status === 0,
          }"
        >
          {{ data?.activity_status_desc }}
        </span>
      </template>
      <template #empty-action>
        <Button label="Refresh" />
      </template>
    </BaseDataTable>
    <div v-if="combinedList?.length < total" class="flex justify-center mt-4">
      <Button label="VIEW MORE" class="btn" :loading="loadingMore" @click="loadMore" />
    </div>
  </div>
</template>

<style>
.p-datatable.activity-log-table {
  .p-datatable-table-container {
    background-color: transparent;
    --p-datatable-header-cell-background: transparent;
    --p-datatable-header-cell-selected-background: transparent;
    --p-datatable-row-background: transparent;
    --p-datatable-body-cell-border-color: var(--colors-gray);
  }
}
</style>

<style scoped lang="scss">
.btn {
  padding: .5rem 2rem;
}
</style>
