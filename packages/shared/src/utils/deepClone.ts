/**
 * 深克隆工具函数
 * 支持处理基本数据类型、对象、数组、Date、RegExp 以及循环引用
 */

/**
 * 深克隆函数，创建对象的完全独立副本
 * @param obj 要克隆的对象
 * @param visited 用于处理循环引用的 WeakMap（内部使用）
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T, visited: WeakMap<object, any> = new WeakMap()): T {
  // 处理基本数据类型和 null
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  // 处理循环引用
  if (visited.has(obj as object)) {
    return visited.get(obj as object)
  }

  // 处理 Date 对象
  if (obj instanceof Date) {
    const clonedDate = new Date(obj.getTime()) as T
    visited.set(obj as object, clonedDate)
    return clonedDate
  }

  // 处理 RegExp 对象
  if (obj instanceof RegExp) {
    const clonedRegExp = new RegExp(obj.source, obj.flags) as T
    visited.set(obj as object, clonedRegExp)
    return clonedRegExp
  }

  // 处理数组
  if (Array.isArray(obj)) {
    const clonedArray: any[] = []
    visited.set(obj as object, clonedArray)
    
    for (let i = 0; i < obj.length; i++) {
      clonedArray[i] = deepClone(obj[i], visited)
    }
    
    return clonedArray as T
  }

  // 处理普通对象
  if (typeof obj === 'object' && obj.constructor === Object) {
    const clonedObj: any = {}
    visited.set(obj as object, clonedObj)
    
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone((obj as any)[key], visited)
      }
    }
    
    return clonedObj as T
  }

  // 处理其他类型的对象（如自定义类实例）
  // 对于复杂对象，我们创建一个新的实例并复制属性
  const clonedObj = Object.create(Object.getPrototypeOf(obj))
  visited.set(obj as object, clonedObj)
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clonedObj[key] = deepClone((obj as any)[key], visited)
    }
  }
  
  return clonedObj as T
}

/**
 * 简化版深克隆函数，用于常见场景
 * 仅处理 JSON 可序列化的数据类型
 * @param obj 要克隆的对象
 * @returns 克隆后的对象
 */
export function simpleDeepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  try {
    return JSON.parse(JSON.stringify(obj))
  } catch (error) {
    console.warn('simpleDeepClone failed, falling back to deepClone:', error)
    return deepClone(obj)
  }
}

/**
 * 检查两个对象是否深度相等
 * @param obj1 第一个对象
 * @param obj2 第二个对象
 * @returns 是否深度相等
 */
export function deepEqual<T>(obj1: T, obj2: T): boolean {
  if (obj1 === obj2) {
    return true
  }

  if (obj1 === null || obj2 === null || typeof obj1 !== 'object' || typeof obj2 !== 'object') {
    return false
  }

  // 处理 Date 对象
  if (obj1 instanceof Date && obj2 instanceof Date) {
    return obj1.getTime() === obj2.getTime()
  }

  // 处理 RegExp 对象
  if (obj1 instanceof RegExp && obj2 instanceof RegExp) {
    return obj1.toString() === obj2.toString()
  }

  // 处理数组
  if (Array.isArray(obj1) && Array.isArray(obj2)) {
    if (obj1.length !== obj2.length) {
      return false
    }
    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i])) {
        return false
      }
    }
    return true
  }

  // 处理对象
  const keys1 = Object.keys(obj1 as object)
  const keys2 = Object.keys(obj2 as object)

  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false
    }
    if (!deepEqual((obj1 as any)[key], (obj2 as any)[key])) {
      return false
    }
  }

  return true
}
