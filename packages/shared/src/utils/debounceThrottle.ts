/**
 * 防抖函数 - 在事件被触发n秒后再执行回调，如果在这n秒内又被触发，则重新计时
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行（第一次触发时立即执行）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate = false,
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let hasExecuted = false

  return function (this: any, ...args: Parameters<T>) {
    const callNow = immediate && !hasExecuted

    // 清除之前的定时器
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }

    if (callNow) {
      func.apply(this, args)
      hasExecuted = true
    }

    timeoutId = setTimeout(() => {
      if (!immediate) {
        func.apply(this, args)
      }
      hasExecuted = false
      timeoutId = null
    }, delay)
  }
}

/**
 * 节流函数 - 规定在一个单位时间内，只能触发一次函数
 * @param func 要节流的函数
 * @param delay 节流时间间隔（毫秒）
 * @param options 配置选项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: {
    leading?: boolean // 是否在开始时执行
    trailing?: boolean // 是否在结束时执行
  } = {},
): (...args: Parameters<T>) => void {
  const { leading = true, trailing = true } = options
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastExecTime = 0
  let lastArgs: Parameters<T> | null = null

  return function (this: any, ...args: Parameters<T>) {
    const currentTime = Date.now()
    lastArgs = args

    // 如果是第一次调用且不需要立即执行
    if (!lastExecTime && !leading) {
      lastExecTime = currentTime
      return
    }

    const remainingTime = delay - (currentTime - lastExecTime)

    if (remainingTime <= 0 || remainingTime > delay) {
      // 可以执行
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      lastExecTime = currentTime
      func.apply(this, args)
    }
    else if (!timeoutId && trailing) {
      // 设置延迟执行
      timeoutId = setTimeout(() => {
        lastExecTime = leading ? Date.now() : 0
        timeoutId = null
        if (lastArgs) {
          func.apply(this, lastArgs)
        }
      }, remainingTime)
    }
  }
}

/**
 * 取消防抖函数的执行
 * @param debouncedFunc 防抖函数
 */
export function cancelDebounce(debouncedFunc: any) {
  if (debouncedFunc && typeof debouncedFunc.cancel === 'function') {
    debouncedFunc.cancel()
  }
}

/**
 * 增强版防抖函数 - 支持取消和立即执行
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行
 * @returns 增强的防抖函数
 */
export function enhancedDebounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate = false,
) {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let hasExecuted = false

  const debouncedFunc = function (this: any, ...args: Parameters<T>) {
    const callNow = immediate && !hasExecuted

    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }

    if (callNow) {
      func.apply(this, args)
      hasExecuted = true
    }

    timeoutId = setTimeout(() => {
      if (!immediate) {
        func.apply(this, args)
      }
      hasExecuted = false
      timeoutId = null
    }, delay)
  }

  // 添加取消方法
  debouncedFunc.cancel = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    hasExecuted = false
  }

  // 添加立即执行方法
  debouncedFunc.flush = function (this: any, ...args: Parameters<T>) {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    func.apply(this, args)
    hasExecuted = false
  }

  return debouncedFunc
}

/**
 * 增强版节流函数 - 支持取消
 * @param func 要节流的函数
 * @param delay 节流时间间隔（毫秒）
 * @param options 配置选项
 * @returns 增强的节流函数
 */
export function enhancedThrottle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: {
    leading?: boolean
    trailing?: boolean
  } = {},
) {
  const { leading = true, trailing = true } = options
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastExecTime = 0
  let lastArgs: Parameters<T> | null = null

  const throttledFunc = function (this: any, ...args: Parameters<T>) {
    const currentTime = Date.now()
    lastArgs = args

    if (!lastExecTime && !leading) {
      lastExecTime = currentTime
      return
    }

    const remainingTime = delay - (currentTime - lastExecTime)

    if (remainingTime <= 0 || remainingTime > delay) {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      lastExecTime = currentTime
      func.apply(this, args)
    }
    else if (!timeoutId && trailing) {
      timeoutId = setTimeout(() => {
        lastExecTime = leading ? Date.now() : 0
        timeoutId = null
        if (lastArgs) {
          func.apply(this, lastArgs)
        }
      }, remainingTime)
    }
  }

  // 添加取消方法
  throttledFunc.cancel = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    lastExecTime = 0
    lastArgs = null
  }

  return throttledFunc
}
