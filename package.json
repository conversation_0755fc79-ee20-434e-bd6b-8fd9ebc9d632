{"name": "bill-merchant-workspace", "type": "module", "version": "1.0.5", "private": true, "scripts": {"dev": "pnpm --filter official dev", "dev:merchant": "pnpm --filter merchant dev", "dev:flexirates": "pnpm --filter flexirates dev", "dev:flexiratesMerchant": "pnpm --filter flexiratesMerchant dev", "dev:all": "concurrently \"pnpm dev\" \"pnpm dev:merchant\" \"pnpm dev:flexirates\" \"pnpm dev:flexiratesMerchant\"", "build": "pnpm build:apps", "build:apps": "pnpm --filter './apps/*' build", "build:apps:test": "pnpm --filter './apps/*' build:test && git add . && git commit -m \"✨ feat(build): global build\"", "build:apps:official:test": "pnpm --filter official build:test && git add . && git commit -m \"✨ feat(build): official build\"", "build:apps:merchant:test": "pnpm --filter merchant build:test && git add . && git commit -m \"✨ feat(build): merchant build\"", "build:apps:flexirates:test": "pnpm --filter flexirates build:test && git add . && git commit -m \"✨ feat(build): flexirates build\"", "build:apps:flexiratesMerchant:test": "pnpm --filter flexiratesMerchant build:test && git add . && git commit -m \"✨ feat(build): flexiratesMerchant build\"", "build:apps:uat-production": "pnpm --filter './apps/*' build:uat-production", "build:apps:official:uat-production": "pnpm --filter official build:uat-production", "build:apps:merchant:uat-production": "pnpm --filter merchant build:uat-production", "build:apps:flexirates:uat-production": "pnpm --filter flexirates build:uat-production", "build:apps:flexiratesMerchant:uat-production": "pnpm --filter flexiratesMerchant build:uat-production", "preview": "pnpm --filter official preview", "preview:dist": "vite preview", "test": "vitest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"vue": "^3.5.13", "vue-router": "4", "vue-i18n": "10", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "primevue": "^4.3.3", "primeicons": "^7.0.0", "@primeuix/styles": "^1.0.1", "@primeuix/themes": "^1.0.1", "@primevue/forms": "^4.3.3", "@floating-ui/vue": "^1.1.6", "vee-validate": "^4.15.0", "@vee-validate/yup": "^4.15.0", "@vee-validate/zod": "^4.15.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "mitt": "^3.0.1", "yup": "^1.6.1", "zod": "^3.24.2", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "@types/nprogress": "^0.2.3", "tailwindcss": "^4.1.3", "@tailwindcss/vite": "^4.1.3"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@primevue/auto-import-resolver": "^4.3.3", "@types/fs-extra": "^11.0.4", "@types/google.maps": "^3.58.1", "@types/node": "^22.14.0", "@types/qrcode": "^1.5.5", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "concurrently": "^8.0.0", "eslint": "^9.21.0", "fs-extra": "^11.3.0", "postcss-import": "^16.1.0", "sass-embedded": "^1.86.3", "tsx": "^4.0.0", "typescript": "~5.7.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.5", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}